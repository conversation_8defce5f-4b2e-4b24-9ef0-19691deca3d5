"""Protocol Consistency Migration Example

This example demonstrates how to migrate existing service clients to use
the new protocol-aware system for consistent handling across local and remote deployments.
"""

import logging
from typing import Optional

from mlopscli.utils.service_clients import ServiceClientFactory, get_service_client_factory
from mlopscli.utils.installation_resolver import InstallationResolver
from mlopscli.utils.service_config import service_registry
from mlopscli.utils.tls_manager import get_tls_manager
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR
from mlopscli.utils.logger import get_logger


class ProtocolAwareKASTManager:
    """
    Example of how to integrate protocol-aware service management into KAST installation.
    
    This demonstrates the migration from hardcoded protocol handling to the new
    centralized, configuration-driven approach.
    """
    
    def __init__(self, installation_location: str, logger: Optional[logging.Logger] = None):
        self._installation_location = installation_location
        self._logger = logger or get_logger(__name__)
        self._service_factory = get_service_client_factory(self._logger)
        self._tls_manager = get_tls_manager(self._logger)
        self._active_services = {}
    
    def setup_keycloak_client(self, admin_user: str = "admin", admin_password: str = "admin") -> any:
        """
        Setup Keycloak client using protocol-aware configuration.
        
        OLD WAY (hardcoded):
        ```python
        if installation_location == "local":
            keycloak_url = "http://127.0.0.1:8080/auth"
        else:
            # Start port forward manually
            keycloak_url = "https://127.0.0.1:8080/auth"
        
        client = Keycloak(
            keycloak_host="127.0.0.1",
            keycloak_port=8080,
            use_https=(installation_location == "remote"),
            ...
        )
        ```
        
        NEW WAY (protocol-aware):
        """
        self._logger.info(f"Setting up Keycloak client for {self._installation_location} deployment")
        
        try:
            # Create protocol-aware Keycloak client
            keycloak_client = self._service_factory.create_keycloak_client(
                installation_location=self._installation_location,
                admin_user=admin_user,
                admin_password=admin_password
            )
            
            # Test connection
            try:
                access_token = keycloak_client.get_access_token()
                self._logger.info("Keycloak client connection verified")
                self._active_services["keycloak"] = keycloak_client
                return keycloak_client
            except Exception as e:
                self._logger.error(f"Keycloak connection test failed: {e}")
                raise
                
        except Exception as e:
            self._logger.error(f"Failed to setup Keycloak client: {e}")
            raise
    
    def setup_minio_client(self, access_key: str = "minio", secret_key: str = "minio123") -> any:
        """
        Setup MinIO client using protocol-aware configuration.
        
        OLD WAY:
        ```python
        if installation_location == "local":
            endpoint = "127.0.0.1:9000"
            secure = False
        else:
            # Manual port forward setup
            endpoint = "127.0.0.1:9000"
            secure = True
        
        client = Minio(endpoint=endpoint, secure=secure, ...)
        ```
        
        NEW WAY:
        """
        self._logger.info(f"Setting up MinIO client for {self._installation_location} deployment")
        
        try:
            minio_client = self._service_factory.create_minio_client(
                installation_location=self._installation_location,
                access_key=access_key,
                secret_key=secret_key
            )
            
            # Test connection
            try:
                # Test with a simple operation
                buckets = minio_client.list_buckets()
                self._logger.info(f"MinIO client connection verified ({len(buckets)} buckets)")
                self._active_services["minio"] = minio_client
                return minio_client
            except Exception as e:
                self._logger.warning(f"MinIO connection test failed (may be expected): {e}")
                # Store client anyway as it might work for actual operations
                self._active_services["minio"] = minio_client
                return minio_client
                
        except Exception as e:
            self._logger.error(f"Failed to setup MinIO client: {e}")
            raise
    
    def setup_generic_service_client(self, service_name: str) -> dict:
        """
        Setup a generic HTTP client for any service.
        
        This is useful for services that don't have dedicated client libraries.
        """
        self._logger.info(f"Setting up generic client for {service_name} ({self._installation_location})")
        
        try:
            client_config = self._service_factory.create_generic_http_client(
                service_name=service_name,
                installation_location=self._installation_location
            )
            
            self._active_services[service_name] = client_config
            return client_config
            
        except Exception as e:
            self._logger.error(f"Failed to setup {service_name} client: {e}")
            raise
    
    def verify_service_connectivity(self, service_name: str) -> bool:
        """
        Verify connectivity to a service using the protocol-aware resolver.
        """
        try:
            endpoint = self._service_factory.get_service_endpoint(
                service_name=service_name,
                installation_location=self._installation_location
            )
            
            self._logger.info(f"Service {service_name} endpoint: {endpoint.url}")
            
            # For HTTPS services, verify certificate
            if endpoint.protocol.value == "https":
                cert_info = self._tls_manager.verify_certificate(
                    hostname=endpoint.host,
                    port=endpoint.port
                )
                
                if cert_info["valid"]:
                    self._logger.info(f"TLS certificate valid for {service_name}")
                elif cert_info["self_signed"]:
                    self._logger.warning(f"Self-signed certificate detected for {service_name}")
                else:
                    self._logger.warning(f"TLS certificate issues for {service_name}: {cert_info['error']}")
            
            return True
            
        except Exception as e:
            self._logger.error(f"Service connectivity check failed for {service_name}: {e}")
            return False
    
    def get_service_endpoints_summary(self) -> dict:
        """Get a summary of all configured service endpoints"""
        summary = {}
        
        for service_name in service_registry.list_all_services():
            try:
                config = service_registry.get_service_config(service_name)
                protocol = config.get_protocol_for_location(self._installation_location)
                
                summary[service_name] = {
                    "namespace": config.namespace,
                    "protocol": protocol.value,
                    "local_port": config.local_port,
                    "service_port": config.service_port,
                    "tls_enabled": config.tls_enabled,
                    "health_check_path": config.health_check_path
                }
            except Exception as e:
                summary[service_name] = {"error": str(e)}
        
        return summary
    
    def cleanup(self) -> None:
        """Clean up all service clients and resources"""
        self._logger.info("Cleaning up protocol-aware KAST manager...")
        
        # Cleanup service factory (this will cleanup all endpoints and port forwards)
        self._service_factory.cleanup_all_clients()
        
        # Clear active services
        self._active_services.clear()
        
        self._logger.info("Cleanup completed")


def demonstrate_migration():
    """Demonstrate the migration from old to new protocol handling"""
    logger = get_logger("migration_demo")
    
    print("=== Protocol Consistency Migration Demo ===\n")
    
    # Test both local and remote configurations
    for location in [INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR]:
        print(f"--- Testing {location.upper()} deployment ---")
        
        manager = ProtocolAwareKASTManager(location, logger)
        
        try:
            # Show service endpoints summary
            print("Service Endpoints Summary:")
            summary = manager.get_service_endpoints_summary()
            for service, info in summary.items():
                if "error" not in info:
                    print(f"  {service}: {info['protocol']}://localhost:{info['local_port']} "
                          f"(namespace: {info['namespace']})")
                else:
                    print(f"  {service}: ERROR - {info['error']}")
            
            print()
            
            # Test service connectivity (only for local to avoid kubectl dependency)
            if location == INSTALLATION_LOCATION_LOCAL_STR:
                print("Testing service connectivity:")
                for service in ["keycloak", "minio", "grafana"]:
                    connected = manager.verify_service_connectivity(service)
                    status = "✓ Connected" if connected else "✗ Failed"
                    print(f"  {service}: {status}")
                
                print()
                
                # Setup example clients
                print("Setting up service clients:")
                try:
                    # Setup generic clients (these will work without actual services running)
                    keycloak_config = manager.setup_generic_service_client("keycloak")
                    print(f"  Keycloak: {keycloak_config['base_url']}")
                    
                    minio_config = manager.setup_generic_service_client("minio")
                    print(f"  MinIO: {minio_config['base_url']}")
                    
                    grafana_config = manager.setup_generic_service_client("grafana")
                    print(f"  Grafana: {grafana_config['base_url']}")
                    
                except Exception as e:
                    print(f"  Client setup error: {e}")
            
            print()
            
        finally:
            # Always cleanup
            manager.cleanup()
    
    print("=== Migration Demo Complete ===")


if __name__ == "__main__":
    demonstrate_migration()
