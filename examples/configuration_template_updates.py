"""Configuration Template Updates for Protocol Consistency

This example shows how to update YAML templates and configuration files
to use the new protocol resolution system instead of hardcoded values.
"""

import yaml
from typing import Dict, Any
from pathlib import Path

from mlopscli.utils.service_config import service_registry
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR


class ConfigurationTemplateUpdater:
    """
    Updates configuration templates to use protocol-aware service resolution.
    
    This class demonstrates how to migrate from hardcoded service URLs
    to dynamic protocol-aware configuration generation.
    """
    
    def __init__(self, installation_location: str):
        self.installation_location = installation_location
    
    def generate_keycloak_config(self) -> Dict[str, Any]:
        """
        Generate Keycloak configuration using protocol-aware settings.
        
        OLD TEMPLATE (hardcoded):
        ```yaml
        keycloak:
          domain: "keycloak.dpsc"
          user: admin
          password: admin
          ingress:
            enabled: true
            appRoot: /auth
          cert:
            secretName: "keycloak-tls-secret"
        ```
        
        NEW TEMPLATE (protocol-aware):
        """
        config = service_registry.get_service_config("keycloak")
        protocol = config.get_protocol_for_location(self.installation_location)
        
        keycloak_config = {
            "keycloak": {
                "domain": config.get_ingress_host(),
                "user": "admin",
                "password": "admin",
                "protocol": protocol.value,
                "port": config.service_port,
                "namespace": config.namespace,
                "ingress": {
                    "enabled": True,
                    "appRoot": "/auth",
                    "protocol": protocol.value
                },
                "tls": {
                    "enabled": config.tls_enabled,
                    "required": config.require_tls,
                    "verify": config.verify_ssl
                }
            }
        }
        
        # Add TLS configuration only if required
        if config.is_tls_required_for_location(self.installation_location):
            keycloak_config["keycloak"]["cert"] = {
                "secretName": "keycloak-tls-secret"
            }
        
        return keycloak_config
    
    def generate_minio_config(self) -> Dict[str, Any]:
        """
        Generate MinIO configuration using protocol-aware settings.
        
        OLD TEMPLATE:
        ```yaml
        minio:
          api:
            tls: true
          console:
            enabled: true
            tls: true
          internaltls:
            enabled: true
        ```
        
        NEW TEMPLATE:
        """
        minio_config = service_registry.get_service_config("minio")
        console_config = service_registry.get_service_config("minio-console")
        
        minio_protocol = minio_config.get_protocol_for_location(self.installation_location)
        console_protocol = console_config.get_protocol_for_location(self.installation_location)
        
        config = {
            "minio": {
                "api": {
                    "protocol": minio_protocol.value,
                    "port": minio_config.service_port,
                    "tls": minio_config.is_tls_required_for_location(self.installation_location)
                },
                "console": {
                    "enabled": True,
                    "protocol": console_protocol.value,
                    "port": console_config.service_port,
                    "tls": console_config.is_tls_required_for_location(self.installation_location)
                },
                "internaltls": {
                    "enabled": minio_config.tls_enabled
                },
                "namespace": minio_config.namespace,
                "ingress": {
                    "api_host": minio_config.get_ingress_host(),
                    "console_host": console_config.get_ingress_host()
                }
            }
        }
        
        return config
    
    def generate_monitoring_config(self) -> Dict[str, Any]:
        """
        Generate monitoring stack configuration (Grafana, Prometheus).
        """
        grafana_config = service_registry.get_service_config("grafana")
        prometheus_config = service_registry.get_service_config("prometheus")
        
        grafana_protocol = grafana_config.get_protocol_for_location(self.installation_location)
        prometheus_protocol = prometheus_config.get_protocol_for_location(self.installation_location)
        
        config = {
            "monitoring": {
                "grafana": {
                    "protocol": grafana_protocol.value,
                    "port": grafana_config.service_port,
                    "namespace": grafana_config.namespace,
                    "ingress_host": grafana_config.get_ingress_host(),
                    "tls_enabled": grafana_config.tls_enabled
                },
                "prometheus": {
                    "protocol": prometheus_protocol.value,
                    "port": prometheus_config.service_port,
                    "namespace": prometheus_config.namespace,
                    "tls_enabled": prometheus_config.tls_enabled
                }
            }
        }
        
        return config
    
    def generate_polycore_config(self) -> Dict[str, Any]:
        """
        Generate Polycore configuration for both gRPC and REST endpoints.
        """
        grpc_config = service_registry.get_service_config("polycore-grpc")
        rest_config = service_registry.get_service_config("polycore-rest")
        
        grpc_protocol = grpc_config.get_protocol_for_location(self.installation_location)
        rest_protocol = rest_config.get_protocol_for_location(self.installation_location)
        
        config = {
            "polycore": {
                "grpc": {
                    "protocol": grpc_protocol.value,
                    "port": grpc_config.service_port,
                    "tls_required": grpc_config.require_tls,
                    "ingress_host": grpc_config.get_ingress_host()
                },
                "rest": {
                    "protocol": rest_protocol.value,
                    "port": rest_config.service_port,
                    "ingress_host": rest_config.get_ingress_host()
                },
                "namespace": grpc_config.namespace,
                "tls_enabled": grpc_config.tls_enabled
            }
        }
        
        return config
    
    def generate_complete_service_config(self) -> Dict[str, Any]:
        """Generate complete service configuration for all services."""
        complete_config = {
            "deployment": {
                "location": self.installation_location,
                "protocol_strategy": "auto"
            },
            "services": {}
        }
        
        # Add all service configurations
        complete_config["services"].update(self.generate_keycloak_config())
        complete_config["services"].update(self.generate_minio_config())
        complete_config["services"].update(self.generate_monitoring_config())
        complete_config["services"].update(self.generate_polycore_config())
        
        # Add service registry summary
        complete_config["service_registry"] = {
            "total_services": len(service_registry.list_all_services()),
            "services_by_type": {}
        }
        
        # Group services by type
        for service_name, config in service_registry.list_all_services().items():
            service_type = config.service_type.value
            if service_type not in complete_config["service_registry"]["services_by_type"]:
                complete_config["service_registry"]["services_by_type"][service_type] = []
            
            complete_config["service_registry"]["services_by_type"][service_type].append({
                "name": service_name,
                "namespace": config.namespace,
                "local_port": config.local_port,
                "service_port": config.service_port
            })
        
        return complete_config
    
    def save_configuration_file(self, config: Dict[str, Any], filename: str) -> str:
        """Save configuration to a YAML file."""
        output_path = Path(f"output/{filename}")
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        return str(output_path)


def demonstrate_template_updates():
    """Demonstrate configuration template updates for both deployment types."""
    print("=== Configuration Template Updates Demo ===\n")
    
    for location in [INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR]:
        print(f"--- {location.upper()} Deployment Configuration ---")
        
        updater = ConfigurationTemplateUpdater(location)
        
        # Generate individual service configurations
        print("1. Keycloak Configuration:")
        keycloak_config = updater.generate_keycloak_config()
        print(yaml.dump(keycloak_config, default_flow_style=False, indent=2))
        
        print("2. MinIO Configuration:")
        minio_config = updater.generate_minio_config()
        print(yaml.dump(minio_config, default_flow_style=False, indent=2))
        
        print("3. Monitoring Configuration:")
        monitoring_config = updater.generate_monitoring_config()
        print(yaml.dump(monitoring_config, default_flow_style=False, indent=2))
        
        # Generate complete configuration
        complete_config = updater.generate_complete_service_config()
        
        # Save to file
        filename = f"kast-config-{location}.yaml"
        try:
            saved_path = updater.save_configuration_file(complete_config, filename)
            print(f"Complete configuration saved to: {saved_path}")
        except Exception as e:
            print(f"Failed to save configuration: {e}")
        
        print(f"Total services configured: {len(service_registry.list_all_services())}")
        print()
    
    print("=== Template Updates Demo Complete ===")


def show_migration_comparison():
    """Show before/after comparison of configuration approaches."""
    print("=== Migration Comparison ===\n")
    
    print("BEFORE (Hardcoded):")
    old_config = """
    keycloak:
      domain: "keycloak.dpsc"
      url: "https://keycloak.dpsc/auth"  # Hardcoded HTTPS
      port: 8080
      
    minio:
      endpoint: "https://minio.dpsc:9000"  # Hardcoded HTTPS
      secure: true  # Hardcoded
      
    grafana:
      url: "https://grafana.admin.dpsc"  # Hardcoded HTTPS
    """
    print(old_config)
    
    print("AFTER (Protocol-Aware):")
    updater = ConfigurationTemplateUpdater(INSTALLATION_LOCATION_REMOTE_STR)
    new_config = {
        "services": {}
    }
    new_config["services"].update(updater.generate_keycloak_config())
    new_config["services"].update(updater.generate_minio_config())
    new_config["services"].update(updater.generate_monitoring_config())
    
    print(yaml.dump(new_config, default_flow_style=False, indent=2))
    
    print("Benefits of the new approach:")
    print("- ✅ Protocol automatically selected based on deployment location")
    print("- ✅ Consistent port management across services")
    print("- ✅ TLS configuration based on service requirements")
    print("- ✅ Centralized service registry for easy updates")
    print("- ✅ No hardcoded URLs or protocols")
    print("- ✅ Automatic port forwarding for remote deployments")


if __name__ == "__main__":
    demonstrate_template_updates()
    print()
    show_migration_comparison()
