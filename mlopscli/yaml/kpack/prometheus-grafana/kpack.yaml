---
components:
  - name: grafana
    version: 2.3.8+7.3.11
    namespace: monitoring
  - name: grafana-dashboards
    version: 1.6.15
    namespace: monitoring
  - name: prometheus
    version: 22.2.1+25.0.0
    namespace: monitoring

global:
  domain: "dpsc"

grafana:
  sidecarsEnabled: true
  _values:
    global:
      imagePullSecrets:
        - name: reg-cred
    grafana:
      plugins:
        - grafana-clickhouse-datasource 4.8.2 # 4.9.0 is bugged
      dashboardProviders:
        dashboardproviders.yaml:
          apiVersion: 1
          providers:
            - name: 'mlops'
              orgId: 1
              folder: 'MLOps Dashboards'
              type: file
              disableDeletion: false
              editable: true
              options:
                path: /var/lib/grafana/dashboards/mlops
      dashboardsConfigMaps:
        mlops: otel-clickhouse-dashboard
      resources:
        limits:
          cpu: "1"
          memory: 2500M
        requests:
          cpu: 100m
          memory: 100M
      extraVolumeMounts:
        - mountPath: /var/lib/grafana-plugins/
          name: grafana-plugins
      extraVolumes:
        - name: grafana-plugins
          emptyDir: {}
      ingress:
        annotations:
          cert-manager.io/cluster-issuer: external-ca-issuer
        tls:
          - hosts:
              - "grafana.admin.dpsc"
              - "grafana.monitoring.svc.cluster.local"
              - "grafana.mlops.prod.k8s.collaborative.vlan"
            secretName: "grafana-tls-secret"
grafana-dashboards: {}

prometheus:
  kubeStateMetrics:
    enabled: true
  _values:
    prometheus:
      server:
        resources:
          limits:
            cpu: "1"
            memory: 2500M
          requests:
            cpu: "1"
            memory: 500M
      configmapReload:
        prometheus:
          resources:
            limits:
              cpu: 100m
              memory: 50M
            requests:
              cpu: 100m
              memory: 50M
      kube-state-metrics:
        resources:
          limits:
            cpu: "1"
            memory: 100M
          requests:
            cpu: 500m
            memory: 50M
