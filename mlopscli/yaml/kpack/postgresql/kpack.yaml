components:
- name: postgresql-operator
  version: 3.0.3+1.14.0
  namespace: sql-store
- name: postgresql
  version: 4.0.0
  namespace: sql-store
global:
  domain: dpsc
  storageClass: standard
  registry: artifactory.thalesdigital.io
  registryPath: ''
postgresql-operator:
  namespace: sql-store
  spiloImageName: kast-project/kast/spilo-17
  spiloVersion: 4.0.4-ubuntu-22.04-202502061011
  adminUser: postgres
  replicationUser: standby
  postgresqlVersion: '15'
  watchedNamespaces: '*'
  podManagementPolicy: parallel
  logLevel: INFO
postgresql:
  domain: dpsc
  namespace: sql-store
  volumeSize: 8Gi
  pgVersion: 15
  adminUser: postgres
  adminPassword:
  replicationUser: standby
  replicationPassword:
  exporterUser: postgres
  exporterPassword:
  teamId: kast
  defaultDb: kast
  defaultDbUser: kast
  instances: 1
  _values:
    server:
      name: kast-default-postgresql
      image: artifactory.thalesdigital.io/kast-project/kast/spilo-17:4.0.4-ubuntu-22.04-202502061011
      defaultDbUserRoles:
      - superuser
      - createdb
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 250m
          memory: 256Mi
      allow_nossl: true
