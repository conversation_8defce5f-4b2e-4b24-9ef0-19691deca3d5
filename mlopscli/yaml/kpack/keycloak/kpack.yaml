---
components:
  - name: keycloak
    version: 1.7.9+2.3.0
    namespace: &auth_ns authentication
  - name: keycloak-realm
    version: 1.6.4
    namespace: *auth_ns

# Note keycloak helm doesn't pull docker image from artifactory

global:
  domain: &domain "dpsc"
  password: &password "{{ genPwd }}"
  sso:
    enabled: true
    domain: &ssodomain !concat ["keycloak.", *domain]
    issuer:
      path: /auth/realms/kast/protocol/openid-connect/
      external: &externalissuer !concat ["https://", *ssodomain]
      internal: &internalissuer !concat ["http://keycloak-http.", *auth_ns]

keycloak-realm:
  _install:
    flags:
      timeout: "10m"
  _values:
    domain: *domain
    createSecurityGroupsOnMasterRealm: true

keycloak:
  domain: *ssodomain
  user: admin  # KEYCLOAK_ADMIN
  password: *password
  ingress:
    enabled: true
    appRoot: /auth
  cert:
    secretName: "keycloak-tls-secret"
  _values:  # configuration injected at chart level
    keycloakx:
      ingress:
        annotations:
          cert-manager.io/cluster-issuer: external-ca-issuer
        tls:
          - hosts:
              - "keycloak.dpsc"
              - "keycloak.authentication.svc.cluster.local"
              - "keycloak.mlops.prod.k8s.collaborative.vlan"
            secretName: "keycloak-tls-secret"
