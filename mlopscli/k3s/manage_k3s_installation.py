import os
from pathlib import Path
import shutil
import subprocess

from mlopscli.kast.manage_gitlab_runner_installation import GITLAB_RUNNER_HELM_REPO_NAME
from mlopscli.kast.manage_kast_installation import CERT_MANAGER_HELM_REPO
from mlopscli.kast.manage_kast_installation import P<PERSON>Y<PERSON>RE_COMPONENT
from mlopscli.kast.manage_kast_installation import ZOT_COMPONENT
from mlopscli.kast.manage_lakefs_installation import LAKEFS_COMPONENT
from mlopscli.kast.manage_otel_installation import OTEL_HELM_REPO_NAME
from mlopscli.utils.constants import ARCH_AMD64
from mlopscli.utils.constants import ARCH_ARM64
from mlopscli.utils.constants import CA_CERT_DIR
from mlopscli.utils.constants import K3S_WORKING_DIR
from mlopscli.utils.constants import WORKING_DIR
from mlopscli.utils.exceptions import UnsupportedArchitectureException
from mlopscli.utils.installation_logger import InstallationLogger
from mlopscli.utils.kubernetes import apply_k8s_component
from mlopscli.utils.kubernetes import uninstall_helm_repo
from mlopscli.utils.manage_base_class import ManageBaseClass
from mlopscli.utils.system import get_arch
from mlopscli.utils.system import is_command_available
from mlopscli.utils.system import log_etc_host_file
from mlopscli.utils.system import run_command
from mlopscli.utils.system import run_sudo_command_with_output

# https://update.k3s.io/v1-release/channels
INSTALL_K3S_VERSION = "v1.31.4+k3s1"
CONTEXT_NAME = "k3s"
REGISTRY_FILE_TEMPLATE = """
configs:
  "zot.dpsc":
    tls:
      insecure_skip_verify: true
"""

POLYFLOW_DIR = os.path.expanduser("~/.polyflow")
POLYFLOW_CERTS_DIR = os.path.join(POLYFLOW_DIR, "certs")


class ManageK3SInstallation(ManageBaseClass):
    def __init__(
        self,
    ) -> None:
        super().__init__(__name__)

    def install_k3s(self) -> None:
        self._logger.info("Installing K3S...")
        log_etc_host_file()
        Path(K3S_WORKING_DIR).mkdir(parents=True, exist_ok=True)
        arch = get_arch()
        self._logger.debug(f"current arch : {arch}")
        if not self._check_requirements(arch):
            self._logger.critical("Mandatory requirements are not fullied")
            raise SystemExit(1)
        else:
            self._logger.info("Mandatory requirements validated")

        if arch == ARCH_ARM64:
            self._deploy_multipass_vm()
        elif arch == ARCH_AMD64:
            if is_command_available("ufw"):
                self._logger.info("disabling the ufw")
                run_sudo_command_with_output("ufw disable", password=self.get_sudo_password())
            else:
                self._logger.debug("ufw disabling not done, as ufw is not a recognized command")

            self._logger.info("Downloading k3s installation scripts...")
            k3s_install_script = f"{K3S_WORKING_DIR}/k3s_install.sh"

            command = f"curl -sfL https://get.k3s.io -o {k3s_install_script}"
            run_command(command)
            run_command(f"chmod 755 {k3s_install_script}")

            self._logger.info("Running installation k3s scripts...")
            env = {}
            env["INSTALL_K3S_VERSION"] = INSTALL_K3S_VERSION

            output = run_sudo_command_with_output(
                command=f"sh {k3s_install_script} --disable=traefik",
                env=env,
                password=self.get_sudo_password(),
            )
            self._logger.info("\n" + output)
            self._validate_k3s_logout(output)
        else:
            raise UnsupportedArchitectureException(arch)

        self._generate_certificates()

        self._add_certificates(arch=arch)

        self._log_k3s_version(arch)

        (_, host_ip) = self.get_hostname_and_ip(arch=arch)
        self._logger.debug("setting the context")
        self._set_context(arch=arch, host_ip=host_ip)
        self._activate_gpu()

        if arch == ARCH_ARM64:
            self._set_etc_hosts_file()
            self._update_etc_hosts_info(host_ip=host_ip)

        self._logger.info("Installing K3S Completed")

    def _add_certificates(self, arch: str) -> None:
        cert_file_local = os.path.join(CA_CERT_DIR, "intermediate-ca", "output", "intermediate-ca-cert.pem")
        if arch == ARCH_ARM64:
            cert_file_remote_tmp = os.path.join("/tmp", "intermediate-ca-cert.pem")  # noqa: S108
            run_sudo_command_with_output(
                f"multipass transfer {cert_file_local} {CONTEXT_NAME}:{cert_file_remote_tmp}", password=self.get_sudo_password()
            )
            run_sudo_command_with_output(
                f"multipass exec {CONTEXT_NAME} -- sudo mv {cert_file_remote_tmp} /etc/ssl/certs/.", password=self.get_sudo_password()
            )
            run_sudo_command_with_output(f"multipass exec {CONTEXT_NAME} -- sudo update-ca-certificates --fresh", password=self.get_sudo_password())
            run_sudo_command_with_output(f"multipass exec {CONTEXT_NAME} -- sudo systemctl restart {CONTEXT_NAME}", password=self.get_sudo_password())
        elif arch == ARCH_AMD64:
            run_sudo_command_with_output(f"cp {cert_file_local} /etc/ssl/certs/.", password=self.get_sudo_password())
            run_sudo_command_with_output("update-ca-certificates --fresh", password=self.get_sudo_password())
            run_sudo_command_with_output(f"systemctl restart {CONTEXT_NAME}", password=self.get_sudo_password())
        else:
            raise UnsupportedArchitectureException(arch)

    def _activate_gpu(self) -> None:
        # absolute path is required to work in local for development and with packaged wheel
        resource_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "gpu", "gpu-operator.yaml")
        self._logger.info("Applying gpu-operator ...")
        apply_k8s_component(resource_file, "gpu-operator")

    def _generate_certificates(self) -> None:
        scripts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "scripts")
        os.chdir(scripts_dir)
        if os.path.exists(os.path.join(scripts_dir, "certs", "root-ca", "output")):
            self._logger.info("Certificates folders already exist. Certificates will not be generated")
        else:
            self._logger.info("Certificates folders don't exist. Generating certificates...")
            run_command(f"./generate-certs.sh {K3S_WORKING_DIR}")

        # copy certificates for polyflow
        shutil.copytree(CA_CERT_DIR, POLYFLOW_CERTS_DIR, dirs_exist_ok=True)

    def _set_etc_hosts_file(self) -> None:
        hosts_file_local_tmp = os.path.join(K3S_WORKING_DIR, "hosts")
        hosts_file_remote_tmp = os.path.join("/tmp", "hosts")  # noqa: S108
        hosts_file_remote = "/etc/hosts"
        multipass_hosts_file_content = run_sudo_command_with_output(
            f"multipass exec {CONTEXT_NAME} -- cat {hosts_file_remote}", password=self.get_sudo_password()
        )

        host_ip = "127.0.0.1"
        new_hosts = {}
        new_hosts["argo.dpsc"] = host_ip
        new_hosts["grafana.admin.dpsc"] = host_ip
        new_hosts["keycloak.dpsc"] = host_ip
        new_hosts["lakefs.dpsc"] = host_ip
        new_hosts["minio-console.dpsc"] = host_ip
        new_hosts["mlflow-processing.dpsc"] = host_ip
        new_hosts["polycore.dpsc"] = host_ip
        new_hosts["zot.dpsc"] = host_ip

        new_lines = []
        if multipass_hosts_file_content is not None:
            lines = multipass_hosts_file_content.splitlines()
            for line in lines:
                parts = line.split()
                if len(parts) > 1:
                    if parts[1] not in new_hosts.keys():
                        new_lines.append(line)
                else:
                    new_lines.append(line)
            for hostname, new_ip in new_hosts.items():
                new_lines.append(f"{new_ip} {hostname} # Managed by mlops-toolchain")
                self._logger.debug(f"Adding host ({hostname}) with IP ({new_ip})")

        with open(hosts_file_local_tmp, "w") as file:
            file.writelines(f"{item}\n" for item in new_lines)

        run_sudo_command_with_output(
            f"multipass transfer {hosts_file_local_tmp} {CONTEXT_NAME}:{hosts_file_remote_tmp}", password=self.get_sudo_password()
        )
        run_sudo_command_with_output(
            f"multipass exec {CONTEXT_NAME} -- sudo mv {hosts_file_remote_tmp} {hosts_file_remote}", password=self.get_sudo_password()
        )

    def _set_registry(self, arch: str) -> None:
        registry_file_content = REGISTRY_FILE_TEMPLATE
        registry_file_dest = "/etc/rancher/k3s/registries.yaml"
        registry_file_local_tmp = os.path.join(K3S_WORKING_DIR, "registries.yaml")
        with open(registry_file_local_tmp, "w") as file:
            file.write(registry_file_content + "\n")
        if arch == ARCH_ARM64:
            registry_file_remote_tmp = os.path.join("/tmp", "registries.yaml")  # noqa: S108
            run_sudo_command_with_output(
                f"multipass transfer {registry_file_local_tmp} {CONTEXT_NAME}:{registry_file_remote_tmp}", password=self.get_sudo_password()
            )
            run_sudo_command_with_output(
                f"multipass exec {CONTEXT_NAME} -- sudo mv {registry_file_remote_tmp} {registry_file_dest}", password=self.get_sudo_password()
            )
            self._logger.info(f"Restarting {CONTEXT_NAME} ...")
            run_sudo_command_with_output(f"multipass exec {CONTEXT_NAME} -- sudo systemctl restart {CONTEXT_NAME}", password=self.get_sudo_password())
        elif arch == ARCH_AMD64:
            run_sudo_command_with_output("mkdir -p /etc/rancher/k3s", password=self.get_sudo_password())
            run_sudo_command_with_output(f"mv {registry_file_local_tmp} {registry_file_dest}", password=self.get_sudo_password())

            self._logger.info(f"Restarting {CONTEXT_NAME} ...")
            run_sudo_command_with_output(f"systemctl restart {CONTEXT_NAME}", password=self.get_sudo_password())
        else:
            raise UnsupportedArchitectureException(arch)

    def _set_context(self, arch: str, host_ip: str) -> None:
        self._clean_context(CONTEXT_NAME)
        if arch == ARCH_ARM64:
            context_content = run_sudo_command_with_output(
                command=f'multipass exec {CONTEXT_NAME} -- bash -c "sudo cat /etc/rancher/k3s/k3s.yaml"',
                password=self.get_sudo_password(),
            )
        elif arch == ARCH_AMD64:
            context_content = run_sudo_command_with_output(command="cat /etc/rancher/k3s/k3s.yaml", password=self.get_sudo_password())
        else:
            raise UnsupportedArchitectureException(arch)

        if context_content is not None:
            context_content = context_content.replace("default", CONTEXT_NAME)
            context_content = context_content.replace("127.0.0.1", host_ip)
            context_dir = "~/.kube"
            context_dir = os.path.expanduser(context_dir)
            Path(context_dir).mkdir(parents=True, exist_ok=True)
            context_file = os.path.join(context_dir, "config")
            with open(context_file, "a") as file:
                file.write(context_content + "\n")

            run_command(f"chmod 600 {context_file}")

    def _validate_k3s_logout(self, log_outputs: str | None) -> None:
        self._logger.debug("validating k3s logout.")
        wanted_logs = ["systemd: Starting k3s", "No change detected so skipping service start"]
        log_found = False
        for wanted_log in wanted_logs:
            if log_outputs is not None and wanted_log in log_outputs:
                log_found = True
                break

        if not log_found:
            self._logger.error(f"failed to validate that k3s log outputs contain: {wanted_logs}")
            self._logger.critical(f"Cannot find wanted log ({wanted_logs})")
            raise SystemExit(1)

    def _deploy_multipass_vm(self) -> None:
        if not self.is_multipass_vm_exist(CONTEXT_NAME):
            run_sudo_command_with_output(
                command=f"multipass launch --name {CONTEXT_NAME} --cpus 10 --memory 12G --disk 40G", password=self.get_sudo_password()
            )
            output = run_sudo_command_with_output(
                command=f'''multipass exec {CONTEXT_NAME} -- bash -c \
                    "curl -sfL https://get.k3s.io | INSTALL_K3S_VERSION={INSTALL_K3S_VERSION} sh -s - --disable=traefik"''',
                password=self.get_sudo_password(),
            )
            self._logger.info(output)
            self._validate_k3s_logout(output)

    def _clean_context(self, context_name: str) -> None:
        run_command(f"kubectl config delete-cluster {context_name}", check=False)
        run_command(f"kubectl config delete-context {context_name}", check=False)
        run_command(f"kubectl config delete-user {context_name}", check=False)

    def _check_requirements(self, arch: str) -> bool:
        _are_requirements_ok = True
        if arch == ARCH_ARM64:
            if is_command_available("multipass"):
                self._logger.info("multipass is installed")
            else:
                self._logger.error("multipass is not installed")
                _are_requirements_ok = False
        elif arch == ARCH_AMD64:
            self._logger.debug("No additional requirements needed for AMD64 architecture")
        else:
            self._logger.error(f"Your architecture is not supported: {arch}")
            _are_requirements_ok = False

        return _are_requirements_ok

    def is_multipass_vm_exist(self, vm_name: str) -> bool:
        if run_sudo_command_with_output(command=f"multipass list | grep {vm_name}", exit_on_error=False, password=self.get_sudo_password()) is None:
            return False
        else:
            return True

    def _update_etc_hosts_info(self, host_ip: str) -> None:
        updates = {}
        updates[CONTEXT_NAME] = host_ip

        self._write_update_etc_hosts(updates)

    def uninstall_k3s(self) -> None:
        self._logger.info("Uninstalling K3S...")
        arch = get_arch()
        if not self._check_requirements(arch):
            self._logger.critical("Mandatory requirements are not fullied.")
            raise SystemExit(1)
        else:
            self._logger.info("Mandatory requirements validated")

        self.get_sudo_password()
        if arch == ARCH_ARM64:
            if self.is_multipass_vm_exist(CONTEXT_NAME):
                run_sudo_command_with_output(command=f"multipass delete {CONTEXT_NAME}", password=self.get_sudo_password())
                run_sudo_command_with_output(command="multipass purge", password=self.get_sudo_password())
                run_command(f"ssh-keygen -R {CONTEXT_NAME}")

                self._logger.debug("removing locally saved k3s version...")
                InstallationLogger.delete_k3s()
            else:
                self._logger.warning(f"no multipass vm found named: '{CONTEXT_NAME}'")
        elif arch == ARCH_AMD64:
            self._logger.info("Executing uninstall script...")
            if os.path.exists("/usr/local/bin/k3s-uninstall.sh"):
                run_sudo_command_with_output(command="/usr/local/bin/k3s-uninstall.sh", password=self.get_sudo_password())

            run_sudo_command_with_output(command="rm -rf /var/lib/rancher", password=self.get_sudo_password())
            run_sudo_command_with_output(command="rm -rf /etc/rancher", password=self.get_sudo_password())
            run_sudo_command_with_output(command="rm -rf /data", password=self.get_sudo_password())
            self._logger.debug("Clearing locally saved k3s version...")
            InstallationLogger.delete_k3s()
        else:
            raise UnsupportedArchitectureException(arch)

        self._delete_certificates()
        self._remove_helm_repo()

        if os.path.exists(WORKING_DIR):
            self._logger.debug("removing working tree.")
            shutil.rmtree(WORKING_DIR)

        self._logger.info("Uninstalling K3S Completed")

    def _delete_certificates(self) -> None:
        self._logger.debug("Deleting certificates...")
        if os.path.exists(CA_CERT_DIR):
            shutil.rmtree(CA_CERT_DIR)
            self._logger.debug(f"Deleted: {CA_CERT_DIR}")

        if os.path.exists(POLYFLOW_CERTS_DIR):
            shutil.rmtree(POLYFLOW_CERTS_DIR)
            self._logger.debug(f"Deleted: {POLYFLOW_CERTS_DIR}")

    def _remove_helm_repo(self) -> None:
        repo_names_list = [
            "kast",
            CERT_MANAGER_HELM_REPO,
            POLYCORE_COMPONENT,
            ZOT_COMPONENT,
            GITLAB_RUNNER_HELM_REPO_NAME,
            LAKEFS_COMPONENT,
            OTEL_HELM_REPO_NAME,
        ]
        for repo_name in repo_names_list:
            uninstall_helm_repo(repo_name)

    def _log_k3s_version(self, arch: str) -> None:
        k3s_version = self._get_k3s_section(arch)
        InstallationLogger.set_k3s_version(k3s_version)

    def _get_k3s_section(self, arch: str) -> str | None:
        version = None
        if arch == ARCH_ARM64:
            version = run_sudo_command_with_output(command="multipass exec k3s -- bash -c 'k3s --version'", password=self.get_sudo_password())
        elif arch == ARCH_AMD64:
            result = subprocess.run(args=["k3s", "--version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            version = " ".join(result.stdout.splitlines())
        else:
            raise UnsupportedArchitectureException(arch)

        return version
