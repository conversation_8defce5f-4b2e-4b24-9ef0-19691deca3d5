"""host_info module"""

import importlib.metadata
import os
import platform
import shutil
import socket
import sys

import psutil

from mlopscli.utils.constants import DEFAULT
from mlopscli.utils.constants import FAILED
from mlopscli.utils.constants import SENSITIVE_VALUES
from mlopscli.utils.constants import UNKNOWN
from mlopscli.utils.exceptions import UnsupportedArchitectureException
from mlopscli.utils.kast_info import KastInfo
from mlopscli.utils.logger import get_logger
from mlopscli.utils.manage_base_class import ManageBaseClass
from mlopscli.utils.system import get_arch

logger = get_logger(__name__)


class HostInfo:
    """_summary_
    Handles all relevant information gathered about the host, in log contexts.
    AVOID initializing multiple of these, as it prints logs.
    """

    _kast = None
    _can_use_psutil = False
    _memory_total_capacity_in_bytes = DEFAULT
    _disk_total_capacity_in_bytes = DEFAULT
    _uname_arch = DEFAULT
    _cli_version = DEFAULT
    _k3s_hostname = DEFAULT
    _hostname_ip = DEFAULT
    _ips = []
    _hostname = DEFAULT
    _processor_name = DEFAULT
    _processor_architecture = DEFAULT
    _processor_architecture_support = DEFAULT
    _architecture_bits = DEFAULT
    _architecture_linkage = DEFAULT
    _platform = DEFAULT
    _os_name = DEFAULT
    _os_version = DEFAULT
    _os_release = DEFAULT
    _python_executable_path = DEFAULT
    _python_version = DEFAULT
    _python_revision = DEFAULT
    _python_build = DEFAULT
    _python_compiler = DEFAULT
    _python_implementation = DEFAULT
    _python_branch = DEFAULT

    @property
    def kast(self) -> KastInfo | None:
        """Gets you the class which contains the specifics of how the Kast version detection went."""
        return self._kast

    @property
    def can_use_psutil(self) -> bool:
        """sets to false if a psutils command fails"""
        return self._can_use_psutil

    @property
    def memory_total_capacity_in_bytes(self) -> str:
        """Check if equals to `FAILED / UNKNOWN`"""
        if not self._can_use_psutil:
            return UNKNOWN

        return self._memory_total_capacity_in_bytes

    @property
    def memory_used_in_pourcent(self) -> str:
        """Updated each call. Check if equals to `FAILED / UNKNOWN`"""
        if not self._can_use_psutil:
            return UNKNOWN

        result = FAILED
        try:
            result = psutil.virtual_memory().percent
        except psutil.AccessDenied as e:
            logger.error("psutil failed with AccessDenied and cannot obtain used memory.", e)
        return str(result)

    @property
    def memory_left_in_bytes(self) -> str:
        """Updated each call. Check if equals to `FAILED / UNKNOWN`"""
        if not self._can_use_psutil:
            return UNKNOWN

        result = FAILED
        try:
            result = psutil.virtual_memory().available
        except psutil.AccessDenied as e:
            logger.error("psutil failed with AccessDenied and cannot obtain memory left.", e)
        return str(result)

    @property
    def disk_total_capacity_in_bytes(self) -> str:
        """in bytes. Check if equals to `FAILED / UNKNOWN`"""
        if not self._can_use_psutil:
            return UNKNOWN

        return self._disk_total_capacity_in_bytes

    @property
    def disk_used_in_pourcent(self) -> str:
        """Updated each call. Check if equals to `FAILED / UNKNOWN`"""
        if not self._can_use_psutil:
            return UNKNOWN

        result = FAILED
        try:
            result = int((shutil.disk_usage("/").used / shutil.disk_usage("/").total) * 100)
        except psutil.AccessDenied as e:
            logger.critical("psutil failed with AccessDenied and cannot obtain disk usage.", e)
        return str(result)

    @property
    def disk_left_in_bytes(self) -> str:
        """Updated each call. Check if not equal to `FAILED / UNKNOWN`"""
        if not self._can_use_psutil:
            return UNKNOWN

        result = FAILED
        try:
            result = psutil.disk_usage("/").free
        except psutil.AccessDenied as e:
            logger.error("psutil failed with AccessDenied and cannot obtain free disk space.", e)
        return str(result)

    @property
    def uname_arch(self) -> str:
        """processor architecture returned from get_arch. Can be different from values returned from platform library."""
        return self._uname_arch

    @property
    def cli_version(self) -> str:
        return self._cli_version

    @property
    def k3s_hostname(self) -> str:
        """hostname in use by k3s. Machines using vms will have it set as `k3s`. Others as the real machine hostnames. Can be set to `FAILED`"""
        return self._k3s_hostname

    @property
    def hostname_ip(self) -> str:
        """main IPv4 address of the CLI's host. Can be set to `FAILED`"""
        return self._hostname_ip

    @property
    def ips(self) -> list[str]:
        """all ips of the hostname. [0] being the cli hostname ip"""
        return self._ips

    @property
    def hostname(self) -> str:
        """the machine's real hostname. Useful to identify if it's the same as the CLI's one."""
        return self._hostname

    @property
    def processor_name(self) -> str:
        """full name of the processor. UNKNOWN if it's the same as the architecture."""
        return self._processor_name

    @property
    def processor_architecture(self) -> str:
        """like get_arch, but handled by the platform library. Could give differing results from get_arch"""
        return self._processor_architecture

    @property
    def processor_architecture_support(self) -> str:
        """identifies if the value returned from the platform library is supported by the CLI. Either `supported` or `not supported`"""
        return self._processor_architecture_support

    @property
    def architecture_bits(self) -> str:
        """32 bits or 64 bits."""
        return self._architecture_bits

    @property
    def architecture_linkage(self) -> str:
        """bit linkage used by the platform"""
        return self._architecture_linkage

    @property
    def platform(self) -> str:
        """full platform description, meant to be readable."""
        return self._platform

    @property
    def os_name(self) -> str:
        """Identifies windows or linux or macOS. Can be UNKNOWN if not found."""
        return self._os_name

    @property
    def os_version(self) -> str:
        return self._os_version

    @property
    def os_release(self) -> str:
        return self._os_release

    @property
    def python_executable_path(self) -> str:
        """complete path to the location of the running python's executable"""
        return self._python_executable_path

    @property
    def python_version(self) -> str:
        return self._python_version

    @property
    def python_revision(self) -> str:
        return self._python_revision

    @property
    def python_build(self) -> str:
        return self._python_build

    @property
    def python_compiler(self) -> str:
        return self._python_compiler

    @property
    def python_implementation(self) -> str:
        return self._python_implementation

    @property
    def python_branch(self) -> str:
        return self._python_branch

    def _fill_values(self) -> None:
        base_class = ManageBaseClass()

        try:
            disk = psutil.disk_usage("/")
            memory = psutil.virtual_memory()

            self._memory_total_capacity_in_bytes = str(memory.total)
            self._disk_total_capacity_in_bytes = str(disk.total)

            self._can_use_psutil = True
        except psutil.AccessDenied as e:
            logger.error("psutil failed with AccessDenied.", e)

        self._uname_arch = get_arch()
        self._cli_version = importlib.metadata.version("cortaix-factory-mlops-mlopscli")

        self._processor_architecture = platform.machine()
        self._processor_name = platform.processor()
        if self._processor_name == self._processor_architecture:
            self._processor_name = ""

        self._architecture_bits = platform.architecture()[0]
        self._architecture_linkage = platform.architecture()[1]
        self._platform = platform.platform()

        self._os_name = platform.system()
        self._os_version = platform.version()
        self._os_release = platform.release()

        self._hostname = socket.gethostname()

        self._python_version = platform.python_version()
        self._python_revision = platform.python_revision()
        self._python_executable_path = os.path.abspath(sys.executable)
        self._python_compiler = platform.python_compiler()
        self._python_implementation = platform.python_implementation()
        self._python_branch = platform.python_branch()
        build_data = platform.python_build()
        self._python_build = build_data[0] + " " + build_data[1]

        if self._uname_arch != "":
            try:
                hostname_and_ip = base_class.get_hostname_and_ip(self._uname_arch)
                self._k3s_hostname = hostname_and_ip[0]
                self._hostname_ip = hostname_and_ip[1]

                if self._k3s_hostname:
                    self._ips = base_class.get_ips(self._k3s_hostname, self._uname_arch)

            except UnsupportedArchitectureException:
                self._k3s_hostname = FAILED
                self._hostname_ip = FAILED
                self._ips = []


    def _handle_empty_string(self, attribute: str) -> None:
        """if attribute is an empty string or worse, gets set to UNKNOWN."""
        value = getattr(self, attribute)

        if value == "":
            setattr(self, attribute, UNKNOWN)

        if value == DEFAULT:
            setattr(self, attribute, UNKNOWN)

    def _parse_after_fill(self) -> None:
        self._handle_empty_string("_k3s_hostname")
        self._handle_empty_string("_hostname_ip")
        self._handle_empty_string("_python_revision")
        self._handle_empty_string("_python_implementation")
        self._handle_empty_string("_python_branch")
        self._handle_empty_string("_os_name")
        self._handle_empty_string("_os_version")
        self._handle_empty_string("_os_release")
        self._handle_empty_string("_architecture_bits")
        self._handle_empty_string("_architecture_linkage")
        self._handle_empty_string("_processor_name")
        self._handle_empty_string("_processor_architecture")
        self._handle_empty_string("_cli_version")
        self._handle_empty_string("_hostname")

    def __init__(self) -> None:
        self._fill_values()
        self._parse_after_fill()


class _HostInfoSingleton:
    initialized = False
    singleton: HostInfo


def get_host_info() -> HostInfo:
    if not _HostInfoSingleton.initialized:
        _HostInfoSingleton.singleton = HostInfo()
        _HostInfoSingleton.initialized = True

    return _HostInfoSingleton.singleton


def get_cli_command() -> str:
    """
    Returns a 'log' safe CLI command, from the brute, full command, given to the CLI.
    Allows you to log the full CLI command without secrets inside of it.
    """
    secrets_suffixes = {"-at", "-au"}
    result = ""
    blur_next = False

    for arg in sys.argv:
        if blur_next and arg not in secrets_suffixes:
            blur_next = False
            SENSITIVE_VALUES.add(arg)
        else:
            if arg in secrets_suffixes:
                blur_next = True
        result = result + " " + arg
    result = result.removeprefix(" ")
    return result
