import subprocess
import threading
import time
from typing import Optional
import signal
import os

from mlopscli.utils.logger import LogColors
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)

PORT_FORWARD_TYPE_POD = "pod"
PORT_FORWARD_TYPE_SERVICE = "service"
PORT_FORWARD_START_WAIT_TIME = 5
PORT_FORWARD_CLEANUP_TIMEOUT = 10


# CODEREVIEW: Can we use a kubectl python sdk instead of subprocess
class PortForwardThread(threading.Thread):
    """
    Enhanced port forwarding thread with improved resource management and error handling.

    Provides reliable kubectl port-forward functionality with:
    - Proper process lifecycle management
    - Graceful shutdown with timeout
    - Connection verification
    - Resource cleanup
    """

    def __init__(
        self,
        type: str,
        name: str,
        namespace: str,
        local_port: int,
        remote_port: int,
        timeout: int = PORT_FORWARD_CLEANUP_TIMEOUT
    ) -> None:
        super().__init__(daemon=True)  # Daemon thread for automatic cleanup
        self._type = type
        self._name = name
        self._local_port = local_port
        self._remote_port = remote_port
        self._namespace = namespace
        self._timeout = timeout
        self._stop_event = threading.Event()  # Event to signal stop
        self._process: Optional[subprocess.Popen] = None  # Subprocess handle
        self._startup_complete = threading.Event()  # Signal when startup is complete
        self._error_message: Optional[str] = None

    def _build_command(self) -> list[str]:
        """Build the kubectl port-forward command"""
        base_cmd = [
            "kubectl",
            "port-forward",
            "--address",
            "127.0.0.1",
        ]

        if self._type == PORT_FORWARD_TYPE_POD:
            target = f"pod/{self._name}"
        elif self._type == PORT_FORWARD_TYPE_SERVICE:
            target = f"service/{self._name}"
        else:
            raise ValueError(f"Unsupported port forward type: {self._type}")

        return base_cmd + [
            target,
            f"{self._local_port}:{self._remote_port}",
            "-n",
            self._namespace,
        ]

    def run(self) -> None:
        """Main thread execution with enhanced error handling and resource management"""
        try:
            cmd = self._build_command()
            logger.debug(f"Starting port forward: {' '.join(cmd)}")

            # Start the kubectl process
            self._process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )  # noqa: S603

            # Signal that startup is complete
            self._startup_complete.set()

            # Monitor the process
            while not self._stop_event.is_set():
                time.sleep(1)  # Free up CPU
                return_code = self._process.poll()

                if return_code is not None:
                    # Process terminated
                    stdout, stderr = self._process.communicate()

                    if return_code != 0:
                        self._error_message = f"Port forward failed (exit {return_code}): {stderr}"
                        logger.debug(
                            f"Port forward terminated with non-zero exit: "
                            f"{LogColors.file_content}{return_code}{LogColors.debug}"
                        )
                        logger.debug(f"stderr: {stderr}")
                    else:
                        logger.debug("Port forward process terminated normally.")

                    self.stop()
                    break

        except Exception as e:
            self._error_message = f"Port forward startup failed: {e}"
            logger.error(f"Port forward error: {e}")
            self._startup_complete.set()  # Signal completion even on error
        finally:
            self._cleanup_process()

    def _cleanup_process(self) -> None:
        """Clean up the subprocess with proper timeout handling"""
        if self._process is not None:
            try:
                if self._process.poll() is None:  # Process still running
                    logger.debug("Terminating port forward process...")
                    self._process.terminate()

                    # Wait for graceful termination
                    try:
                        self._process.wait(timeout=5)
                        logger.debug("Port forward process terminated gracefully.")
                    except subprocess.TimeoutExpired:
                        logger.warning("Port forward process did not terminate gracefully, killing...")
                        self._process.kill()
                        self._process.wait()
                        logger.debug("Port forward process killed.")

            except Exception as e:
                logger.error(f"Error during port forward cleanup: {e}")
            finally:
                self._process = None

    def stop(self) -> None:
        """Stop the port forwarding thread gracefully"""
        logger.debug("Stopping port forward thread...")
        self._stop_event.set()  # Set the stop event to True

    def wait_for_startup(self, timeout: float = 10.0) -> bool:
        """
        Wait for the port forward to start up successfully.

        Returns:
            True if startup completed successfully, False if timeout or error
        """
        return self._startup_complete.wait(timeout=timeout)

    def get_error_message(self) -> Optional[str]:
        """Get any error message from the port forward process"""
        return self._error_message

    def is_healthy(self) -> bool:
        """Check if the port forward is running and healthy"""
        return (
            self.is_alive() and
            self._process is not None and
            self._process.poll() is None and
            self._error_message is None
        )

    def get_local_endpoint(self) -> str:
        """Get the local endpoint URL for this port forward"""
        return f"127.0.0.1:{self._local_port}"

    def __enter__(self):
        """Context manager entry"""
        self.start()
        if not self.wait_for_startup():
            raise RuntimeError(f"Port forward startup failed: {self._error_message}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup"""
        self.stop()
        self.join(timeout=self._timeout)
