import subprocess
import threading
import time

from mlopscli.utils.logger import LogColors
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)

PORT_FORWARD_TYPE_POD = "pod"
PORT_FORWARD_TYPE_SERVICE = "service"
PORT_FORWARD_START_WAIT_TIME = 5


# CODEREVIEW: Can we use a kubectl python sdk instead of subprocess
class PortForwardThread(threading.Thread):
    def __init__(self, type: str, name: str, namespace: str, local_port: int, remote_port: int) -> None:
        super().__init__()
        self._type = type
        self._name = name
        self._local_port = local_port
        self._remote_port = remote_port
        self._namespace = namespace
        self._stop_event = threading.Event()  # Event to signal stop
        self._process = None  # Placeholder for subprocess

    def run(self) -> None:
        if self._type == PORT_FORWARD_TYPE_POD:
            cmd = [
                "kubectl",
                "port-forward",
                "--address",
                "127.0.0.1",
                f"pod/{self._name}",
                f"{self._local_port}:{self._remote_port}",
                "-n",
                self._namespace,
            ]
        elif self._type == PORT_FORWARD_TYPE_SERVICE:
            cmd = [
                "kubectl",
                "port-forward",
                "--address",
                "127.0.0.1",
                f"service/{self._name}",
                f"{self._local_port}:{self._remote_port}",
                "-n",
                self._namespace,
            ]
        else:
            raise ValueError(f"Unsupported type ({self._type})")

        self._process = subprocess.Popen(cmd)  # noqa: S603

        try:
            # Keep the thread alive until a stop signal is received
            while not self._stop_event.is_set():
                time.sleep(1)  # Free up CPU
                return_code = self._process.poll()
                if return_code is not None:
                    logger.debug("Port forward process terminated by itself.")
                    if return_code != 0:
                        logger.debug(f"port forward terminated with non 0 exit: {LogColors.file_content}{return_code}")
                    else:
                        logger.debug("process terminated with return code 0.")
                    self.stop()
        finally:
            # Terminate subprocess when stop signal is received
            if self._process is not None:  # Make sure process is initiated
                self._process.terminate()  # Gracefully terminate the process
                self._process.wait()  # Wait for the process to terminate
                logger.debug("after wait")

    def stop(self) -> None:
        self._stop_event.set()  # Set the stop event to True
