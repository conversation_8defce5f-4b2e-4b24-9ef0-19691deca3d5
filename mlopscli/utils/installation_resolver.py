import logging
import subprocess
import time
from typing import Any
from typing import Type

from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR
from mlopscli.utils.constants import INSTALLATION_LOCATION_REMOTE_STR
from mlopscli.utils.port_forward_thread import PORT_FORWARD_START_WAIT_TIME
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_POD
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_SERVICE
from mlopscli.utils.port_forward_thread import PortForwardThread


class InstallationResolver:
    """
    Resolves service endpoints and port-forwarding based on installation location.
    Use this to instantiate service clients (e.g., Keycloak, LakeFS, etc) in a location-agnostic way.
    """

    def __init__(self, logger: logging.Logger) -> None:
        self._logger = logger

    def _verify_service_exists(self, service_name: str, namespace: str) -> None:
        """Verify that a service exists in the specified namespace."""
        try:
            result = subprocess.run(["kubectl", "get", "svc", service_name, "-n", namespace], capture_output=True, text=True, timeout=10)  # noqa: S603, S607
            return result.returncode == 0
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            return False

    def resolve_service(
        self,
        service_name: str,
        installation_location: str,
        local_host: str,
        local_port: int,
        remote_host: str,
        remote_port: int,
        namespace: str,
        client_cls: Type[Any],
        **client_kwargs: str,
    ) -> None:
        """
        Returns a client instance for the given service, port-forwarding if remote.
        """
        if installation_location == INSTALLATION_LOCATION_LOCAL_STR:
            self._logger.info(f"[Resolver] Using local endpoint for {service_name}: {local_host}:{local_port}")
            return client_cls(keycloak_host=local_host, keycloak_port=local_port, **client_kwargs)
        elif installation_location == INSTALLATION_LOCATION_REMOTE_STR:
            self._logger.info(f"[Resolver] Starting port-forward for {service_name}...")

            # Map service names to their actual service names in Kubernetes
            service_name_mapping = {
                "keycloak": "keycloak-http",
                "lakefs": "lakefs",
                "prometheus": "prometheus",
                "grafana": "grafana",
                "clickhouse": "clickhouse",
            }

            k8s_service_name = service_name_mapping.get(service_name, service_name)

            # Verify service exists before attempting port forward
            if not self._verify_service_exists(k8s_service_name, namespace):
                self._logger.warning(f"[Resolver] Service {k8s_service_name} not found in namespace {namespace}. Falling back to pod forwarding.")
                # Fallback to pod forwarding if service doesn't exist
                pf_thread = PortForwardThread(
                    type=PORT_FORWARD_TYPE_POD,
                    name=f"{service_name}-0",  # Common StatefulSet naming convention
                    namespace=namespace,
                    local_port=local_port,
                    remote_port=remote_port,
                )
            else:
                pf_thread = PortForwardThread(
                    type=PORT_FORWARD_TYPE_SERVICE,
                    name=k8s_service_name,
                    namespace=namespace,
                    local_port=local_port,
                    remote_port=remote_port,
                )

            pf_thread.start()
            self._logger.info(f"[Resolver] Awaiting port forwarding for {service_name}...")
            time.sleep(PORT_FORWARD_START_WAIT_TIME)
            if not pf_thread.is_alive():
                self._logger.warning(f"[Resolver] Port forward for {service_name} failed or already running.")
            else:
                self._logger.info(f"[Resolver] Port forwarding for {service_name} established.")
            return client_cls(keycloak_host=remote_host, keycloak_port=local_port, **client_kwargs)
        else:
            raise ValueError(f"Invalid installation location: {installation_location}")
