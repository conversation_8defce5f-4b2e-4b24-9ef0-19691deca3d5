"""Comprehensive Secret Cleanup Manager for Component Uninstallation

This module provides centralized secret management and cleanup capabilities
following MCStack principles for safe, verifiable, and reversible operations.
"""

import json
import os
import shutil
import time
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
import yaml

from mlopscli.secrets.keyring_manager import KeyringManager
from mlopscli.utils.constants import WORKING_DIR
from mlopscli.utils.kubernetes import uninstall_k8s_component, is_component_installed
from mlopscli.utils.logger import get_logger


class SecretType(Enum):
    """Types of secrets managed by the system"""
    KEYRING = "keyring"
    KUBERNETES = "kubernetes"
    FILE = "file"
    ENVIRONMENT = "environment"


@dataclass
class SecretReference:
    """Reference to a secret with its location and metadata"""
    secret_type: SecretType
    name: str
    location: str  # keyring service, k8s namespace, file path, etc.
    username: Optional[str] = None  # For keyring secrets
    description: Optional[str] = None
    critical: bool = False  # Whether this secret is critical for system operation
    
    def get_identifier(self) -> str:
        """Get unique identifier for this secret"""
        if self.secret_type == SecretType.KEYRING:
            return f"keyring:{self.location}:{self.username or 'default'}"
        elif self.secret_type == SecretType.KUBERNETES:
            return f"k8s:{self.location}:{self.name}"
        elif self.secret_type == SecretType.FILE:
            return f"file:{self.location}"
        else:
            return f"{self.secret_type.value}:{self.location}:{self.name}"


@dataclass
class ComponentSecretDefinition:
    """Complete definition of secrets managed by a component"""
    component_name: str
    secrets: List[SecretReference]
    dependencies: List[str]  # Components that depend on these secrets
    dependents: List[str]   # Components this component depends on
    description: str = ""
    
    def get_secrets_by_type(self, secret_type: SecretType) -> List[SecretReference]:
        """Get all secrets of a specific type"""
        return [s for s in self.secrets if s.secret_type == secret_type]
    
    def get_critical_secrets(self) -> List[SecretReference]:
        """Get all critical secrets"""
        return [s for s in self.secrets if s.critical]


class SecretBackupManager:
    """Manages backup and restore of secrets during cleanup operations"""
    
    def __init__(self, backup_dir: Optional[str] = None):
        self._backup_dir = Path(backup_dir or os.path.join(WORKING_DIR, "secret_backups"))
        self._backup_dir.mkdir(parents=True, exist_ok=True)
        self._logger = get_logger(__name__)
        self._keyring = KeyringManager()
    
    def create_backup(self, component_name: str, secrets: List[SecretReference]) -> str:
        """Create a backup of component secrets"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_id = f"{component_name}_{timestamp}"
        backup_path = self._backup_dir / f"{backup_id}.json"
        
        backup_data = {
            "backup_id": backup_id,
            "component": component_name,
            "timestamp": timestamp,
            "secrets": []
        }
        
        for secret in secrets:
            secret_backup = {
                "reference": asdict(secret),
                "value": None,
                "backed_up": False
            }
            
            try:
                if secret.secret_type == SecretType.KEYRING:
                    value = self._keyring.get_secret(secret.location, secret.username or "default")
                    if value:
                        secret_backup["value"] = value
                        secret_backup["backed_up"] = True
                elif secret.secret_type == SecretType.FILE:
                    if os.path.exists(secret.location):
                        with open(secret.location, 'r') as f:
                            secret_backup["value"] = f.read()
                        secret_backup["backed_up"] = True
                # Note: K8s secrets are not backed up for security reasons
                
            except Exception as e:
                self._logger.warning(f"Failed to backup secret {secret.name}: {e}")
            
            backup_data["secrets"].append(secret_backup)
        
        # Save backup
        with open(backup_path, 'w') as f:
            json.dump(backup_data, f, indent=2)
        
        self._logger.info(f"Secret backup created: {backup_id}")
        return backup_id
    
    def restore_backup(self, backup_id: str) -> bool:
        """Restore secrets from a backup"""
        backup_path = self._backup_dir / f"{backup_id}.json"
        
        if not backup_path.exists():
            self._logger.error(f"Backup not found: {backup_id}")
            return False
        
        try:
            with open(backup_path, 'r') as f:
                backup_data = json.load(f)
            
            restored_count = 0
            for secret_data in backup_data["secrets"]:
                if not secret_data["backed_up"] or not secret_data["value"]:
                    continue
                
                secret_ref = SecretReference(**secret_data["reference"])
                
                try:
                    if secret_ref.secret_type == SecretType.KEYRING:
                        success = self._keyring.store_secret(
                            secret_ref.location, 
                            secret_ref.username or "default", 
                            secret_data["value"]
                        )
                        if success:
                            restored_count += 1
                    elif secret_ref.secret_type == SecretType.FILE:
                        os.makedirs(os.path.dirname(secret_ref.location), exist_ok=True)
                        with open(secret_ref.location, 'w') as f:
                            f.write(secret_data["value"])
                        restored_count += 1
                        
                except Exception as e:
                    self._logger.error(f"Failed to restore secret {secret_ref.name}: {e}")
            
            self._logger.info(f"Restored {restored_count} secrets from backup {backup_id}")
            return restored_count > 0
            
        except Exception as e:
            self._logger.error(f"Failed to restore backup {backup_id}: {e}")
            return False
    
    def list_backups(self, component_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """List available backups"""
        backups = []
        
        for backup_file in self._backup_dir.glob("*.json"):
            try:
                with open(backup_file, 'r') as f:
                    backup_data = json.load(f)
                
                if component_name and backup_data.get("component") != component_name:
                    continue
                
                backups.append({
                    "backup_id": backup_data["backup_id"],
                    "component": backup_data["component"],
                    "timestamp": backup_data["timestamp"],
                    "secret_count": len(backup_data["secrets"]),
                    "file_path": str(backup_file)
                })
                
            except Exception as e:
                self._logger.warning(f"Failed to read backup file {backup_file}: {e}")
        
        return sorted(backups, key=lambda x: x["timestamp"], reverse=True)


class ComponentSecretRegistry:
    """Central registry of component secrets with dependency tracking"""
    
    def __init__(self):
        self._logger = get_logger(__name__)
        self._components: Dict[str, ComponentSecretDefinition] = {}
        self._initialize_component_definitions()
    
    def _initialize_component_definitions(self) -> None:
        """Initialize component secret definitions"""
        
        # PostgreSQL - Critical database component
        self.register_component(ComponentSecretDefinition(
            component_name="postgresql",
            description="PostgreSQL database server with admin credentials",
            secrets=[
                SecretReference(
                    secret_type=SecretType.KEYRING,
                    name="postgres_admin_password",
                    location="postgres",
                    username="postgres",
                    description="PostgreSQL admin password",
                    critical=True
                ),
                SecretReference(
                    secret_type=SecretType.KUBERNETES,
                    name="postgres-secret",
                    location="mlops-toolchain",
                    description="PostgreSQL connection secret for Polycore",
                    critical=True
                )
            ],
            dependencies=[],  # PostgreSQL is a base dependency
            dependents=["lakefs", "polycore", "keycloak"]
        ))
        
        # Keycloak - Authentication service
        self.register_component(ComponentSecretDefinition(
            component_name="keycloak",
            description="Keycloak authentication service",
            secrets=[
                SecretReference(
                    secret_type=SecretType.KEYRING,
                    name="keycloak_admin_password",
                    location="keycloak",
                    username="keycloak",
                    description="Keycloak admin password",
                    critical=False
                ),
                SecretReference(
                    secret_type=SecretType.KUBERNETES,
                    name="keycloak-tls-secret",
                    location="authentication",
                    description="Keycloak TLS certificate",
                    critical=False
                )
            ],
            dependencies=["postgresql"],
            dependents=[]
        ))
        
        # MinIO - Object storage
        self.register_component(ComponentSecretDefinition(
            component_name="minio",
            description="MinIO object storage service",
            secrets=[
                SecretReference(
                    secret_type=SecretType.KEYRING,
                    name="minio_root_password",
                    location="minio",
                    username="minio",
                    description="MinIO root password",
                    critical=True
                ),
                SecretReference(
                    secret_type=SecretType.KUBERNETES,
                    name="minio-tls-secret",
                    location="object-store",
                    description="MinIO TLS certificate",
                    critical=False
                )
            ],
            dependencies=[],
            dependents=["lakefs", "argo", "zot"]
        ))
        
        # LakeFS - Data lake management
        self.register_component(ComponentSecretDefinition(
            component_name="lakefs",
            description="LakeFS data lake management",
            secrets=[
                SecretReference(
                    secret_type=SecretType.KUBERNETES,
                    name="lakefs-postgres-secret",
                    location="data-lake",
                    description="LakeFS PostgreSQL connection secret",
                    critical=True
                ),
                SecretReference(
                    secret_type=SecretType.FILE,
                    name="lakefs_credentials",
                    location=os.path.join(WORKING_DIR, "lakefs-creds.yaml"),
                    description="LakeFS API credentials file",
                    critical=False
                )
            ],
            dependencies=["postgresql", "minio"],
            dependents=["argo"]
        ))
        
        # ClickHouse - Analytics database
        self.register_component(ComponentSecretDefinition(
            component_name="clickhouse",
            description="ClickHouse analytics database",
            secrets=[
                SecretReference(
                    secret_type=SecretType.KEYRING,
                    name="clickhouse_admin_password",
                    location="clickhouse",
                    username="admin",
                    description="ClickHouse admin password",
                    critical=False
                ),
                SecretReference(
                    secret_type=SecretType.KUBERNETES,
                    name="clickhouse-tls-secret",
                    location="olap-store",
                    description="ClickHouse TLS certificate",
                    critical=False
                )
            ],
            dependencies=[],
            dependents=["otel"]
        ))
        
        # Argo Workflows - Processing engine
        self.register_component(ComponentSecretDefinition(
            component_name="argo",
            description="Argo Workflows processing engine",
            secrets=[
                SecretReference(
                    secret_type=SecretType.KUBERNETES,
                    name="argo-s3",
                    location="default",
                    description="Argo S3 access secret",
                    critical=True
                ),
                SecretReference(
                    secret_type=SecretType.KUBERNETES,
                    name="intermediate-ca",
                    location="default",
                    description="Intermediate CA certificate",
                    critical=False
                ),
                SecretReference(
                    secret_type=SecretType.KUBERNETES,
                    name="lakefs-secret",
                    location="default",
                    description="LakeFS access secret for Argo",
                    critical=False
                )
            ],
            dependencies=["minio", "lakefs"],
            dependents=[]
        ))
    
    def register_component(self, component_def: ComponentSecretDefinition) -> None:
        """Register a component secret definition"""
        self._components[component_def.component_name] = component_def
        self._logger.debug(f"Registered component: {component_def.component_name}")
    
    def get_component_definition(self, component_name: str) -> Optional[ComponentSecretDefinition]:
        """Get component secret definition"""
        return self._components.get(component_name)
    
    def get_all_components(self) -> Dict[str, ComponentSecretDefinition]:
        """Get all registered components"""
        return self._components.copy()
    
    def get_dependent_components(self, component_name: str) -> List[str]:
        """Get components that depend on the given component"""
        if component_name not in self._components:
            return []
        
        return self._components[component_name].dependents
    
    def validate_safe_removal(self, component_name: str) -> Tuple[bool, List[str]]:
        """Check if component secrets can be safely removed"""
        dependents = self.get_dependent_components(component_name)
        
        # Check if any dependent components are still installed
        active_dependents = []
        for dependent in dependents:
            # This would need to be implemented based on component detection logic
            # For now, we'll assume all dependents are potentially active
            active_dependents.append(dependent)
        
        return len(active_dependents) == 0, active_dependents


# Global registry instance
secret_registry = ComponentSecretRegistry()
