import base64
import datetime
import os
import subprocess
import time

from kubernetes import client
from kubernetes import config
import yaml

from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR
from mlopscli.utils.constants import INSTALLATION_LOCATION_REMOTE_STR
from mlopscli.utils.logger import get_logger
from mlopscli.utils.system import run_command
from mlopscli.utils.system import run_command_with_output

logger = get_logger(__name__)

CERTIFICATE_TEMPLATE = {
    "apiVersion": "cert-manager.io/v1",
    "kind": "Certificate",
    "metadata": {},
    "spec": {"dnsNames": [], "issuerRef": {"group": "cert-manager.io", "kind": "ClusterIssuer", "name": "external-ca-issuer"}},
}


def restart_deployment(deployment_name: str, namespace: str) -> None:
    config.load_kube_config()
    apps_v1 = client.AppsV1Api()
    patch = {"spec": {"template": {"metadata": {"annotations": {"kubectl.kubernetes.io/restartedAt": datetime.datetime.now().isoformat()}}}}}

    try:
        apps_v1.patch_namespaced_deployment(deployment_name, namespace, patch)
        logger.debug(f"Deployment '{deployment_name}' in namespace '{namespace}' restarted successfully.")
    except client.exceptions.ApiException as e:
        if e.status == 404:
            logger.error(f"Deployment '{deployment_name}' not found in namespace '{namespace}'.")
        else:
            logger.error(f"Error occurred while trying to restart deployment: {e}")


def scale_statefulset(statefulset_name: str, namespace: str, scale_size: int) -> None:
    config.load_kube_config()
    apps_v1 = client.AppsV1Api()
    try:
        # Get the current state of the StatefulSet
        statefulset = apps_v1.read_namespaced_stateful_set(statefulset_name, namespace)
        original_scale = statefulset.spec.replicas
        logger.debug(f"Scaling StatefulSet '{statefulset_name}' in namespace '{namespace}' from {original_scale} to {scale_size} replicas.")

        statefulset.spec.replicas = scale_size
        apps_v1.patch_namespaced_stateful_set(statefulset_name, namespace, statefulset)

        while True:
            logger.debug(f"Waiting for StatefulSet '{statefulset_name}' in namespace '{namespace}' to scale to {scale_size} replicas.")
            updated_statefulset = apps_v1.read_namespaced_stateful_set(statefulset_name, namespace)
            # scaling to 0 case
            if scale_size == 0:
                if updated_statefulset.status.replicas is None or updated_statefulset.status.replicas == 0:
                    logger.debug(f"StatefulSet '{statefulset_name}' has been scaled to 0.")
                    break
            else:
                if updated_statefulset.status.replicas == scale_size:
                    logger.debug(f"StatefulSet '{statefulset_name}' has been scaled to {scale_size}.")
                    break

            logger.debug(
                f"Waiting for StatefulSet '{statefulset_name}' to scale to {scale_size} ... Current replicas: {updated_statefulset.status.replicas}"
            )
            time.sleep(5)  # Polling interval

    except client.exceptions.ApiException as e:
        if e.status == 404:
            logger.error(f"StatefulSet '{statefulset_name}' not found in namespace '{namespace}'.")
        else:
            logger.error(f"Error occurred while trying to scale StatefulSet: {e}")


def scale_deployment(deployment_name: str, namespace: str, scale_size: int) -> None:
    config.load_kube_config()
    apps_v1 = client.AppsV1Api()
    try:
        # Get the current state of the deployment
        deployment = apps_v1.read_namespaced_deployment(deployment_name, namespace)
        original_scale = deployment.spec.replicas
        logger.debug(f"Scaling deployment '{deployment_name}' in namespace '{namespace}' from {original_scale} to {scale_size} replicas.")
        deployment.spec.replicas = scale_size
        apps_v1.patch_namespaced_deployment(deployment_name, namespace, deployment)

        while True:
            logger.debug(f"Waiting deployment '{deployment_name}' in namespace '{namespace}' to scale to {scale_size} replicas.")
            updated_deployment = apps_v1.read_namespaced_deployment(deployment_name, namespace)
            # scaling to 0 case
            if scale_size == 0:
                if updated_deployment.status.replicas is None:
                    logger.debug(f"Deployment '{deployment_name}' has been scaled to 0.")
                    break
            else:
                if updated_deployment.status.replicas == scale_size:
                    logger.debug(f"Deployment '{deployment_name}' has been scaled to {scale_size}.")
                    break

            logger.debug(
                f"Waiting for deployment '{deployment_name}' to scale to {scale_size} ... Current replicas: {updated_deployment.status.replicas}"
            )
            time.sleep(5)  # Polling interval

    except client.exceptions.ApiException as e:
        if e.status == 404:
            logger.error(f"Deployment '{deployment_name}' not found in namespace '{namespace}'.")
        else:
            logger.error(f"Error occurred while trying to scale deployment: {e}")


def scale_statefulset_down_and_up(statefulset_name: str, namespace: str, must_exist: bool) -> None:
    config.load_kube_config()
    apps_v1 = client.AppsV1Api()

    try:
        # Get the current state of the statefulset
        statefulset = apps_v1.read_namespaced_stateful_set(statefulset_name, namespace)
        original_replicas = statefulset.spec.replicas

        # scale down
        scale_statefulset(statefulset_name=statefulset_name, namespace=namespace, scale_size=0)
        # scale back up
        scale_statefulset(statefulset_name=statefulset_name, namespace=namespace, scale_size=original_replicas)

    except client.exceptions.ApiException as e:
        if e.status == 404:
            if must_exist:
                logger.error(f"Statefulset '{statefulset_name}' not found in namespace '{namespace}'.")
            else:
                logger.debug(f"Statefulset '{statefulset_name}' not found in namespace '{namespace}'.")
        else:
            logger.error(f"Error occurred while trying to scale statefulset: {e}")


def scale_deployment_down_and_up(deployment_name: str, namespace: str, must_exist: bool) -> None:
    config.load_kube_config()
    apps_v1 = client.AppsV1Api()

    try:
        # Get the current state of the deployment
        deployment = apps_v1.read_namespaced_deployment(deployment_name, namespace)
        original_replicas = deployment.spec.replicas

        # scale down
        scale_deployment(deployment_name=deployment_name, namespace=namespace, scale_size=0)
        # scale back up
        scale_deployment(deployment_name=deployment_name, namespace=namespace, scale_size=original_replicas)
    except client.exceptions.ApiException as e:
        if e.status == 404:
            if must_exist:
                logger.error(f"Deployment '{deployment_name}' not found in namespace '{namespace}'.")
            else:
                logger.debug(f"Deployment '{deployment_name}' not found in namespace '{namespace}'.")
        else:
            logger.error(f"Error occurred while trying to scale deployment: {e}")


def wait_for_pod_to_run(namespace: str, pod_name: str, timeout: int = 300) -> None:
    """
    Waits for the specified pod to reach the Running status.

    :param namespace: The namespace where the pod is located.
    :param pod_name: The name of the pod to check.
    :param timeout: The maximum time (in seconds) to wait for the pod to be Running.
    :raises TimeoutError: If the pod does not reach Running status within the timeout period.
    """

    # Get the Kubernetes API client
    config.load_kube_config()
    v1 = client.CoreV1Api()

    start_time = time.time()

    while True:
        # Fetch the pod information
        pod = v1.read_namespaced_pod(name=pod_name, namespace=namespace)

        pod_status = pod.status.phase
        logger.debug(f"Current status of pod '{pod_name}' in namespace '{namespace}': {pod_status}")

        if pod_status == "Running":
            logger.debug(f"Pod '{pod_name}' is now Running.")
            # EM: don't check subcontainer status yet, this will break image downloader.
            return
        elif pod_status in ["Succeeded", "Failed"]:
            raise RuntimeWarning(f"Pod '{pod_name}' is in '{pod_status}' state. It cannot reach Running.")

        # Check for timeout
        if time.time() - start_time > timeout:
            raise TimeoutError(f"Timed out waiting for pod '{pod_name}' to be Running.")

        # Wait for a while before checking again
        time.sleep(5)


def wait_for_pod_with_prefix_to_run(namespace: str, pod_name_prefix: str, timeout: int = 300) -> None:
    """
    Waits for the specified pod(s) with the given prefix to reach the Running status.

    :param namespace: The namespace where the pods are located.
    :param pod_name_prefix: The prefix of the pod names to check.
    :param timeout: The maximum time (in seconds) to wait for the pod to be Running.
    :raises TimeoutError: If no matching pod reaches Running status within the timeout period.
    :raises RuntimeWarning: If a matching pod is in Succeeded or Failed state.
    """

    # Load the Kubernetes configuration
    config.load_kube_config()
    v1 = client.CoreV1Api()

    start_time = time.time()

    while True:
        # List all pods in the namespace
        pods = v1.list_namespaced_pod(namespace=namespace)

        # Filter pods with the matching prefix
        matching_pods = [pod for pod in pods.items if pod.metadata.name.startswith(pod_name_prefix)]

        if not matching_pods:
            logger.debug(f"No pods found with prefix '{pod_name_prefix}' in namespace '{namespace}'.")
            time.sleep(5)
            continue  # Wait and check again if no matching pods are found

        for pod in matching_pods:
            pod_status = pod.status.phase
            logger.debug(f"Current status of pod '{pod.metadata.name}' in namespace '{namespace}': {pod_status}")

            if pod_status == "Running":
                all_running = all(container.ready for container in pod.status.container_statuses)

                if all_running:
                    logger.debug(f"All containers in pod '{pod.metadata.name}' are now Running.")
                    return
                else:
                    logger.debug(f"Not all containers in pod '{pod.metadata.name}' are Running")
            elif pod_status in ["Succeeded", "Failed"]:
                raise RuntimeWarning(f"Pod '{pod.metadata.name}' is in '{pod_status}' state. It cannot reach Running.")

        # Check for timeout
        if time.time() - start_time > timeout:
            raise TimeoutError(f"Timed out waiting for pods with prefix '{pod_name_prefix}' to be Running.")

        # Wait for a while before checking again
        time.sleep(5)


def wait_for_service(namespace: str, service_name: str, timeout: int = 300, interval: int = 2) -> None:
    config.load_kube_config()
    v1 = client.CoreV1Api()

    service_found = False
    end_time = time.time() + timeout
    while time.time() < end_time:
        try:
            v1.read_namespaced_service(name=service_name, namespace=namespace)
            logger.debug(f"Service '{service_name}' in namespace '{namespace}' is found.")
            service_found = True
            break
        except client.exceptions.ApiException as e:
            if e.status == 404:
                logger.debug(f"Service '{service_name}' not found, waiting...")
            else:
                logger.error(f"An error occurred: {e}")
                break

        time.sleep(interval)

    if not service_found:
        logger.warning(f"Timeout: Service '{service_name}' was not found in namespace '{namespace}' within {timeout} seconds.")


def create_pod(pod_name: str, image: str, registry_secret_name: str, namespace: str, node_name: str | None = None) -> None:
    config.load_kube_config()
    v1 = client.CoreV1Api()

    try:
        logger.debug(f"Creating pod {pod_name} ...")
        # Create the pod specification
        pod_spec = client.V1Pod(
            metadata=client.V1ObjectMeta(name=pod_name, namespace=namespace),
            spec=client.V1PodSpec(
                containers=[client.V1Container(name=pod_name, image=image)],
                image_pull_secrets=[client.V1LocalObjectReference(name=registry_secret_name)],
                node_name=node_name,
            ),
        )

        # Create the pod in Kubernetes
        v1.create_namespaced_pod(namespace=namespace, body=pod_spec)
        logger.debug(f"Pod {pod_name} created on {node_name}.")

    except client.exceptions.ApiException as e:
        logger.error(f"Exception when creating pods: {e}")


def force_download_image_on_host(
    images: list[str], registry_secret_name: str, namespace: str, artifactory_username: str, artifactory_token: str, server: str
) -> None:
    config.load_kube_config()
    v1 = client.CoreV1Api()
    logger.info("Downloading images ...")

    try:
        if not is_component_installed(type="secret", name=registry_secret_name, namespace=namespace):
            create_docker_registry_secret(
                name=registry_secret_name,
                artifactory_username=artifactory_username,
                artifactory_token=artifactory_token,
                namespace=namespace,
                server=server,
            )
        # Get the list of nodes
        nodes = [node.metadata.name for node in v1.list_node().items]

        # Iterate over each node
        for node in nodes:
            logger.debug(f"Creating pods on node: {node}")

            # Iterate over each image
            for image in images:
                image_name_cleaned = image.split("/")[-1].replace(":", "-").replace(".", "-").replace("_", "-")
                pod_name = f"{image_name_cleaned}-pod-{node.replace('.', '-')}"  # Replace dots in node name
                pod_name = pod_name[:63]
                pod_name = pod_name.rstrip("-")

                create_pod(pod_name=pod_name, image=image, registry_secret_name=registry_secret_name, namespace=namespace, node_name=node)
                wait_for_pod_to_run(namespace=namespace, pod_name=pod_name)
                delete_pod(pod_name=pod_name, namespace=namespace)

        logger.info("All images have been downloaded")

    except client.exceptions.ApiException as e:
        logger.error(f"Exception while downloading images: {e}")


def delete_pod(pod_name: str, namespace: str) -> None:
    config.load_kube_config()
    core_v1 = client.CoreV1Api()

    try:
        logger.debug(f"Deleting pod '{pod_name}' ...")
        core_v1.delete_namespaced_pod(name=pod_name, namespace=namespace)

        logger.debug(f"Pod '{pod_name}' deleted from namespace '{namespace}' successfully.")

    except client.exceptions.ApiException as e:
        if e.status == 404:
            logger.error(f"Pod '{pod_name}' not found in namespace '{namespace}'.")
        else:
            logger.error(f"Error occurred while trying to delete pod '{pod_name}': {e}")


def create_docker_registry_secret(
    name: str, server: str, artifactory_username: str, artifactory_token: str, namespace: str, force_create: bool = False
) -> bool:
    config.load_kube_config()
    core_v1 = client.CoreV1Api()
    if is_component_installed(type="secret", name=name, namespace=namespace) and force_create:
        core_v1.delete_namespaced_secret(name=name, namespace=namespace)

    if not is_component_installed(type="secret", name=name, namespace=namespace):
        auth = f"{artifactory_username}:{artifactory_token}"
        encoded_auth = base64.b64encode(auth.encode()).decode()

        # Prepare the authentication information
        docker_config_json = {
            ".dockerconfigjson": base64.b64encode(
                f'{{"auths":{{"{server}":{{"username":"{artifactory_username}","password":"{artifactory_token}","email":"{artifactory_username}","auth":"{encoded_auth}"}}}}}}'.encode()
            ).decode()
        }

        secret_body = client.V1Secret(
            api_version="v1",
            kind="Secret",
            metadata=client.V1ObjectMeta(name=name, namespace=namespace),
            type="kubernetes.io/dockerconfigjson",
            data=docker_config_json,
        )

        try:
            core_v1.create_namespaced_secret(namespace=namespace, body=secret_body)
            logger.debug(f"Secret '{name}' created successfully in namespace '{namespace}'.")
            return True
        except client.exceptions.ApiException as e:
            logger.error(f"Failed to create secret '{name}': {e}")
            return False
    else:
        return True


def is_component_installed(type: str, name: str, namespace: str = "") -> bool:
    logger.debug(f"Checking if component: '{type}' named '{name}' in namespace '{namespace}' is installed...")
    try:
        if namespace == "":
            result = subprocess.run(
                args=["kubectl", "get", type, "--no-headers"],  # noqa: S607
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True,
                text=True,
            )
        else:
            result = subprocess.run(
                args=["kubectl", "get", type, "-n", namespace, "--no-headers"],  # noqa: S607
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True,
                text=True,
            )

        # Get the list of installed releases
        installed_components = result.stdout.strip().split()

        # Check if the desired release name is in the installed releases
        return name in installed_components

    except subprocess.CalledProcessError as e:
        logger.error(f"Error occurred while checking components: {e.stderr}")
        return False


def is_helm_chart_installed(name: str) -> bool:
    try:
        # Execute the Helm command to list releases
        result = subprocess.run(
            args=["helm", "list", "-A", "--short"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True,
            text=True,
        )

        # Get the list of installed releases
        installed_releases = result.stdout.strip().split()

        # Check if the desired release name is in the installed releases
        return name in installed_releases

    except subprocess.CalledProcessError as e:
        logger.error(f"Error occurred while checking Helm releases: {e.stderr}")
        return False


def create_secret(secret_name: str, secret_data: dict[str, str], namespace: str, force_create: bool = False) -> None:
    config.load_kube_config()
    core_v1 = client.CoreV1Api()
    if is_component_installed(type="secret", name=secret_name, namespace=namespace) and force_create:
        core_v1.delete_namespaced_secret(name=secret_name, namespace=namespace)

    if not is_component_installed(type="secret", name=secret_name, namespace=namespace):
        encoded_data = {key: base64.b64encode(value.encode()).decode() for key, value in secret_data.items()}

        secret_body = client.V1Secret(
            api_version="v1", kind="Secret", metadata=client.V1ObjectMeta(name=secret_name, namespace=namespace), type="Opaque", data=encoded_data
        )

        try:
            logger.debug(f"Creating secret '{secret_name}' in namespace '{namespace}'.")
            core_v1.create_namespaced_secret(namespace=namespace, body=secret_body)
            logger.debug(f"Secret '{secret_name}' created successfully in namespace '{namespace}'.")
        except client.exceptions.ApiException as e:
            logger.error(f"Failed to create secret '{secret_name}': {e}")


def check_image_pull_secret(service_account_name: str, namespace: str, secret_name: str) -> bool:
    config.load_kube_config()
    v1 = client.CoreV1Api()

    try:
        service_account = v1.read_namespaced_service_account(service_account_name, namespace)
        image_pull_secrets = service_account.image_pull_secrets
        if image_pull_secrets is None:
            is_reg_cred_present = False
        else:
            is_reg_cred_present = any(secret.name == secret_name for secret in image_pull_secrets)

        return is_reg_cred_present

    except client.exceptions.ApiException as e:
        logger.error(f"Exception when calling CoreV1Api->read_namespaced_service_account: {e}")
        return False


def is_helm_repo_installed(name: str) -> bool:
    """Identifies if helm repo add was ran for that specific component or not."""
    helm_repo_list = run_command_with_output("helm repo list 2> /dev/null || true")

    found = False
    for line in helm_repo_list.splitlines():
        if line.startswith(name):
            found = True
            break
    return found


def uninstall_helm_chart(name: str, namespace: str) -> None:
    if is_helm_chart_installed(name):
        logger.info(f"Uninstalling chart {name}...")
        command = f"helm uninstall {name} -n {namespace}"
        run_command(command)
        logger.info(f"Uninstalling {name} completed")
    else:
        logger.info(f"No chart {name} installed")


def uninstall_helm_repo(name: str) -> None:
    if is_helm_repo_installed(name):
        logger.info(f"Removing helm repo: ({name})...")
        run_command(f"helm repo remove {name}")
        logger.info("Repo removal completed.")
    else:
        logger.info(f"No repo named {name} installed.")


def uninstall_k8s_component(type: str, name: str, namespace: str = "") -> None:
    if is_component_installed(type=type, name=name, namespace=namespace):
        logger.info(f"Uninstalling {type} {namespace}/{name}...")
        if namespace == "":
            command = f"kubectl delete {type} {name}"
        else:
            command = f"kubectl delete {type} {name} -n {namespace}"
        run_command(command)
        logger.info(f"Uninstalling {type} {namespace}/{name} completed")
    else:
        logger.info(f"No component {type} {namespace}/{name} installed")


def create_certificate(name: str, namespace: str, dns_names: list, yaml_file_path: str) -> None:
    if is_component_installed(type="secret", name=name, namespace=namespace):
        logger.debug(f"Secret ({name}) already exist")
    else:
        certificate_values = CERTIFICATE_TEMPLATE
        certificate_values["metadata"]["name"] = name
        certificate_values["metadata"]["namespace"] = namespace
        certificate_values["spec"]["secretName"] = name
        for dns in dns_names:
            certificate_values["spec"]["dnsNames"].append(dns)

        yaml_file_path = os.path.join(yaml_file_path, f"{name}.yaml")
        with open(yaml_file_path, "w") as file:
            logger.debug(f"writing certificate {name} yaml values...")
            yaml.dump(certificate_values, file, default_flow_style=False)
            file.close()

        apply_k8s_component(yaml_file_path)


def apply_k8s_component(file: str, namespace: str = "") -> None:
    if namespace == "":
        command = f"kubectl apply -f {file}"
    else:
        command = f"kubectl apply -f {file} -n {namespace}"
    run_command(command)


def configure_storage_class(installation_location: str, component: str) -> str:
    """Get the recommended storage class for the current cluster environment."""
    # First check if local-path exists
    if installation_location == INSTALLATION_LOCATION_LOCAL_STR:
        if check_storage_class_exists(get_default_storage_class()):
            return get_default_storage_class()
    # Check if smbdynamicstorage exists for postgresql
    if installation_location == INSTALLATION_LOCATION_REMOTE_STR and component == "postgresql":
        if check_storage_class_exists("smbdynamicstorage-postgres"):
            return "smbdynamicstorage-postgres"
        else:
            return "smbdynamicstorage"

    # Check for other common storage classes
    common_storage_classes = ["standard", "hostpath", "local-path", "local-storage", "smbdynamicstorage"]
    for sc in common_storage_classes:
        if check_storage_class_exists(sc):
            return sc
    # Fallback to default storage class if nothing else works
    return get_default_storage_class()


def check_storage_class_exists(storage_class_name: str) -> bool:
    """Check if a storage class exists in the cluster."""
    config.load_kube_config()
    storage_v1 = client.StorageV1Api()

    try:
        storage_v1.read_storage_class(name=storage_class_name)
        logger.debug(f"Storage class '{storage_class_name}' exists.")
        return True
    except client.exceptions.ApiException as e:
        if e.status == 404:
            logger.debug(f"Storage class '{storage_class_name}' not found.")
            return False
        else:
            logger.error(f"Error checking storage class '{storage_class_name}': {e}")
            return False


def get_default_storage_class() -> str | None:
    """Get the default storage class in the cluster."""
    config.load_kube_config()
    storage_v1 = client.StorageV1Api()

    try:
        storage_classes = storage_v1.list_storage_class()
        for sc in storage_classes.items:
            if sc.metadata.annotations and sc.metadata.annotations.get("storageclass.kubernetes.io/is-default-class") == "true":
                logger.debug(f"Default storage class found: {sc.metadata.name}")
                return sc.metadata.name

        logger.debug("No default storage class found.")
        return None
    except client.exceptions.ApiException as e:
        logger.error(f"Error getting default storage class: {e}")
        return None
