import os

ARCH_ARM64 = "arm64"
ARCH_AMD64 = "x86_64"
ARTIFACTORY_REGISTRY_URL = "artifactory.thalesdigital.io"
ARTIFACTORY_SECRET_NAME = "reg-cred"  # noqa: S105
SUPPORTED_ARCH = [ARCH_AMD64, ARCH_ARM64]
LOG_DIR = os.path.expanduser("~/Library/Logs")
WORKING_DIR = os.path.expanduser("~/.cache/mlopscli")
KAST_WORKING_DIR = os.path.join(WORKING_DIR, "kast_installation")
K3S_WORKING_DIR = os.path.join(WORKING_DIR, "k3s_installation")
TMP_WORKING_DIR = os.path.join(WORKING_DIR, "tmp")
DEFAULT_LOG_FILE = os.path.join(LOG_DIR, "mlops.log")
PACKAGE_FILE = os.path.join(TMP_WORKING_DIR, "mlops-logs.tgz")
CA_CERT_DIR = os.path.join(K3S_WORKING_DIR, "certs")

INSTALLATION_LOCATION_LOCAL_STR = "local"
INSTALLATION_LOCATION_REMOTE_STR = "remote"
INSTALLATION_LOCATIONS = [INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR]

DEFAULT_MLOPS_DIR = os.path.expanduser("~/mlops")

UNKNOWN = "unknown"
FAILED = "failed"
DEFAULT = "default"

SENSITIVE_VALUES = set()
SENSITIVE_KEYS = [
    "access_key_id",
    "accesskey",
    "authEncryptSecretKey",
    "databaseConnectionString",
    "existingSecret",
    "kast_artifactory_token",
    "kast_artifactory_user",
    "password",
    "postgres_db_password",
    "secret_access_key",
    "secretkey",
    "s3_root_password",
    "s3_secret_key",
]
OBFUSCATED_REPLACEMENT_VALUE = "*****OBFUSCATED*****"
