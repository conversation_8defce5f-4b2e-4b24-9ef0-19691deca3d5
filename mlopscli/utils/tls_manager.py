"""TLS/Certificate Management for Protocol Consistency

This module provides centralized TLS certificate handling and validation
across local and remote deployments following MCStack principles.
"""

import os
import ssl
import socket
import logging
from typing import Optional, Dict, Any, Tuple
from pathlib import Path
import tempfile
import subprocess

from mlopscli.utils.constants import CA_CERT_DIR
from mlopscli.utils.logger import get_logger


class TLSManager:
    """
    Centralized TLS certificate management with MCStack compliance.
    
    Provides:
    - Certificate validation and verification
    - Self-signed certificate handling
    - CA certificate management
    - SSL context creation
    - Certificate trust store management
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self._logger = logger or get_logger(__name__)
        self._ca_cert_dir = Path(CA_CERT_DIR)
        self._ca_cert_dir.mkdir(parents=True, exist_ok=True)
        self._trust_store: Dict[str, str] = {}
        self._ssl_contexts: Dict[str, ssl.SSLContext] = {}
    
    def verify_certificate(self, hostname: str, port: int, timeout: int = 10) -> Dict[str, Any]:
        """
        Verify SSL certificate for a given hostname and port.
        
        Args:
            hostname: Target hostname
            port: Target port
            timeout: Connection timeout in seconds
            
        Returns:
            Dictionary with certificate information and validation status
        """
        result = {
            "valid": False,
            "self_signed": False,
            "expired": False,
            "hostname_match": False,
            "issuer": None,
            "subject": None,
            "not_before": None,
            "not_after": None,
            "error": None
        }
        
        try:
            # Create SSL context for verification
            context = ssl.create_default_context()
            
            # Connect and get certificate
            with socket.create_connection((hostname, port), timeout=timeout) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    
                    if cert:
                        result.update({
                            "valid": True,
                            "issuer": dict(x[0] for x in cert.get('issuer', [])),
                            "subject": dict(x[0] for x in cert.get('subject', [])),
                            "not_before": cert.get('notBefore'),
                            "not_after": cert.get('notAfter'),
                            "hostname_match": ssl.match_hostname(cert, hostname) is None
                        })
                        
                        # Check if self-signed
                        issuer = result["issuer"]
                        subject = result["subject"]
                        if issuer and subject:
                            result["self_signed"] = (
                                issuer.get("commonName") == subject.get("commonName") and
                                issuer.get("organizationName") == subject.get("organizationName")
                            )
                    
        except ssl.SSLError as e:
            result["error"] = f"SSL Error: {e}"
            if "certificate verify failed" in str(e).lower():
                result["self_signed"] = True
        except socket.timeout:
            result["error"] = f"Connection timeout to {hostname}:{port}"
        except Exception as e:
            result["error"] = f"Connection error: {e}"
        
        return result
    
    def create_ssl_context(
        self, 
        verify_ssl: bool = True, 
        ca_cert_path: Optional[str] = None,
        client_cert_path: Optional[str] = None,
        client_key_path: Optional[str] = None
    ) -> ssl.SSLContext:
        """
        Create SSL context with appropriate verification settings.
        
        Args:
            verify_ssl: Whether to verify SSL certificates
            ca_cert_path: Path to CA certificate file
            client_cert_path: Path to client certificate file
            client_key_path: Path to client key file
            
        Returns:
            Configured SSL context
        """
        if verify_ssl:
            context = ssl.create_default_context()
        else:
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
        
        # Load custom CA certificate if provided
        if ca_cert_path and os.path.exists(ca_cert_path):
            try:
                context.load_verify_locations(ca_cert_path)
                self._logger.debug(f"Loaded CA certificate from {ca_cert_path}")
            except Exception as e:
                self._logger.warning(f"Failed to load CA certificate: {e}")
        
        # Load client certificate if provided
        if client_cert_path and client_key_path:
            if os.path.exists(client_cert_path) and os.path.exists(client_key_path):
                try:
                    context.load_cert_chain(client_cert_path, client_key_path)
                    self._logger.debug("Loaded client certificate")
                except Exception as e:
                    self._logger.warning(f"Failed to load client certificate: {e}")
        
        return context
    
    def add_ca_certificate(self, cert_name: str, cert_content: str) -> bool:
        """
        Add a CA certificate to the trust store.
        
        Args:
            cert_name: Name for the certificate
            cert_content: PEM-encoded certificate content
            
        Returns:
            True if certificate was added successfully
        """
        try:
            cert_path = self._ca_cert_dir / f"{cert_name}.crt"
            
            with open(cert_path, 'w') as f:
                f.write(cert_content)
            
            self._trust_store[cert_name] = str(cert_path)
            self._logger.info(f"Added CA certificate: {cert_name}")
            return True
            
        except Exception as e:
            self._logger.error(f"Failed to add CA certificate {cert_name}: {e}")
            return False
    
    def get_ca_bundle_path(self) -> Optional[str]:
        """
        Get path to CA certificate bundle.
        
        Returns:
            Path to CA bundle file or None if not available
        """
        # Check for system CA bundle locations
        ca_bundle_locations = [
            "/etc/ssl/certs/ca-certificates.crt",  # Debian/Ubuntu
            "/etc/pki/tls/certs/ca-bundle.crt",    # RHEL/CentOS
            "/etc/ssl/ca-bundle.pem",              # SUSE
            "/usr/local/share/certs/ca-root-nss.crt",  # FreeBSD
        ]
        
        for bundle_path in ca_bundle_locations:
            if os.path.exists(bundle_path):
                return bundle_path
        
        # Check for custom CA bundle in our cert directory
        custom_bundle = self._ca_cert_dir / "ca-bundle.crt"
        if custom_bundle.exists():
            return str(custom_bundle)
        
        return None
    
    def create_ca_bundle(self) -> Optional[str]:
        """
        Create a CA certificate bundle from all trusted certificates.
        
        Returns:
            Path to created CA bundle or None if failed
        """
        try:
            bundle_path = self._ca_cert_dir / "ca-bundle.crt"
            
            with open(bundle_path, 'w') as bundle_file:
                # Add system CA certificates if available
                system_bundle = self.get_ca_bundle_path()
                if system_bundle and system_bundle != str(bundle_path):
                    with open(system_bundle, 'r') as sys_bundle:
                        bundle_file.write(sys_bundle.read())
                        bundle_file.write('\n')
                
                # Add custom CA certificates
                for cert_name, cert_path in self._trust_store.items():
                    if os.path.exists(cert_path):
                        with open(cert_path, 'r') as cert_file:
                            bundle_file.write(f"# {cert_name}\n")
                            bundle_file.write(cert_file.read())
                            bundle_file.write('\n')
            
            self._logger.info(f"Created CA bundle: {bundle_path}")
            return str(bundle_path)
            
        except Exception as e:
            self._logger.error(f"Failed to create CA bundle: {e}")
            return None
    
    def validate_certificate_chain(self, cert_path: str, ca_path: Optional[str] = None) -> bool:
        """
        Validate a certificate chain using OpenSSL.
        
        Args:
            cert_path: Path to certificate file
            ca_path: Path to CA certificate file
            
        Returns:
            True if certificate chain is valid
        """
        try:
            cmd = ["openssl", "verify"]
            
            if ca_path:
                cmd.extend(["-CAfile", ca_path])
            
            cmd.append(cert_path)
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=30,
                check=False
            )
            
            if result.returncode == 0:
                self._logger.debug(f"Certificate validation successful: {cert_path}")
                return True
            else:
                self._logger.warning(f"Certificate validation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self._logger.error("Certificate validation timed out")
            return False
        except Exception as e:
            self._logger.error(f"Certificate validation error: {e}")
            return False
    
    def get_certificate_info(self, cert_path: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a certificate file.
        
        Args:
            cert_path: Path to certificate file
            
        Returns:
            Dictionary with certificate information or None if failed
        """
        try:
            cmd = [
                "openssl", "x509", "-in", cert_path, 
                "-text", "-noout", "-subject", "-issuer", "-dates"
            ]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=30,
                check=True
            )
            
            # Parse the output (simplified)
            output = result.stdout
            info = {
                "subject": None,
                "issuer": None,
                "not_before": None,
                "not_after": None,
                "valid": True
            }
            
            for line in output.split('\n'):
                line = line.strip()
                if line.startswith('subject='):
                    info["subject"] = line[8:]
                elif line.startswith('issuer='):
                    info["issuer"] = line[7:]
                elif line.startswith('notBefore='):
                    info["not_before"] = line[10:]
                elif line.startswith('notAfter='):
                    info["not_after"] = line[9:]
            
            return info
            
        except subprocess.CalledProcessError as e:
            self._logger.error(f"Failed to get certificate info: {e}")
            return None
        except Exception as e:
            self._logger.error(f"Certificate info error: {e}")
            return None
    
    def cleanup(self) -> None:
        """Clean up resources and temporary files"""
        self._ssl_contexts.clear()
        self._logger.debug("TLS manager cleanup completed")


# Global TLS manager instance
_global_tls_manager: Optional[TLSManager] = None


def get_tls_manager(logger: Optional[logging.Logger] = None) -> TLSManager:
    """Get or create the global TLS manager instance"""
    global _global_tls_manager
    
    if _global_tls_manager is None:
        _global_tls_manager = TLSManager(logger)
    
    return _global_tls_manager
