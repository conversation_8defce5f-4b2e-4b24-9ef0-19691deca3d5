"""Service Configuration Registry for Protocol Consistency

This module provides centralized service configuration management for consistent
protocol handling across local and remote deployments following MCStack principles.
"""

from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional

from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR


class ProtocolScheme(Enum):
    """Supported protocol schemes"""
    HTTP = "http"
    HTTPS = "https"
    GRPC = "grpc"
    GRPCS = "grpcs"  # gRPC with TLS


class ServiceType(Enum):
    """Service type classifications"""
    AUTHENTICATION = "authentication"
    DATA_LAKE = "data_lake"
    OBJECT_STORAGE = "object_storage"
    MONITORING = "monitoring"
    ANALYTICS = "analytics"
    PROCESSING = "processing"
    REGISTRY = "registry"
    INGRESS = "ingress"


@dataclass
class ServiceEndpointConfig:
    """Unified service endpoint configuration following MCStack governance principles"""

    # Service identification (required fields first)
    service_name: str
    service_type: ServiceType
    namespace: str
    service_port: int
    local_port: int  # Port for local port-forwarding

    # Protocol configuration per deployment type
    local_protocol: ProtocolScheme = ProtocolScheme.HTTP
    remote_protocol: ProtocolScheme = ProtocolScheme.HTTPS

    # TLS and security configuration
    tls_enabled: bool = True
    verify_ssl: bool = False  # For self-signed certificates
    require_tls: bool = False  # Whether TLS is mandatory

    # Service discovery overrides
    k8s_service_name: Optional[str] = None
    ingress_host: Optional[str] = None

    # Connection and reliability settings
    timeout: int = 30
    retry_attempts: int = 3
    health_check_path: str = "/health"
    connection_check_enabled: bool = True

    # Port forwarding configuration
    port_forward_type: str = "service"  # "service" or "pod"
    fallback_to_pod: bool = True
    
    def get_k8s_service_name(self) -> str:
        """Get the actual Kubernetes service name"""
        return self.k8s_service_name or self.service_name
    
    def get_protocol_for_location(self, installation_location: str) -> ProtocolScheme:
        """Get the appropriate protocol for the deployment location"""
        if installation_location == INSTALLATION_LOCATION_LOCAL_STR:
            return self.local_protocol
        elif installation_location == INSTALLATION_LOCATION_REMOTE_STR:
            return self.remote_protocol
        else:
            raise ValueError(f"Invalid installation location: {installation_location}")
    
    def get_ingress_host(self) -> str:
        """Get the ingress hostname for the service"""
        return self.ingress_host or f"{self.service_name}.dpsc"
    
    def is_tls_required_for_location(self, installation_location: str) -> bool:
        """Check if TLS is required for the given deployment location"""
        if self.require_tls:
            return True
        
        protocol = self.get_protocol_for_location(installation_location)
        return protocol in [ProtocolScheme.HTTPS, ProtocolScheme.GRPCS]


class ServiceConfigRegistry:
    """Central registry for all service configurations with MCStack compliance"""
    
    def __init__(self):
        self._services: Dict[str, ServiceEndpointConfig] = {}
        self._initialize_default_services()
    
    def _initialize_default_services(self) -> None:
        """Initialize default service configurations based on current KAST setup"""
        
        # Authentication Services
        self.register_service(ServiceEndpointConfig(
            service_name="keycloak",
            service_type=ServiceType.AUTHENTICATION,
            namespace="authentication",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=8080,
            local_port=8080,
            k8s_service_name="keycloak-http",
            ingress_host="keycloak.dpsc",
            health_check_path="/auth/health",
            tls_enabled=True,
            verify_ssl=False
        ))
        
        # Data Lake Services
        self.register_service(ServiceEndpointConfig(
            service_name="lakefs",
            service_type=ServiceType.DATA_LAKE,
            namespace="data-lake",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=8000,
            local_port=8000,
            ingress_host="lakefs.dpsc",
            health_check_path="/api/v1/healthcheck",
            tls_enabled=True,
            verify_ssl=False
        ))
        
        # Object Storage Services
        self.register_service(ServiceEndpointConfig(
            service_name="minio",
            service_type=ServiceType.OBJECT_STORAGE,
            namespace="object-store",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=9000,
            local_port=9000,
            k8s_service_name="s3",
            ingress_host="minio.dpsc",
            health_check_path="/minio/health/live",
            tls_enabled=True,
            verify_ssl=False
        ))
        
        self.register_service(ServiceEndpointConfig(
            service_name="minio-console",
            service_type=ServiceType.OBJECT_STORAGE,
            namespace="object-store",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=9001,
            local_port=9001,
            ingress_host="minio-console.dpsc",
            health_check_path="/",
            tls_enabled=True,
            verify_ssl=False
        ))
        
        # Monitoring Services
        self.register_service(ServiceEndpointConfig(
            service_name="grafana",
            service_type=ServiceType.MONITORING,
            namespace="monitoring",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=3000,
            local_port=3000,
            ingress_host="grafana.admin.dpsc",
            health_check_path="/api/health",
            tls_enabled=True,
            verify_ssl=False
        ))
        
        self.register_service(ServiceEndpointConfig(
            service_name="prometheus",
            service_type=ServiceType.MONITORING,
            namespace="monitoring",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=9090,
            local_port=9090,
            health_check_path="/-/healthy",
            tls_enabled=True,
            verify_ssl=False
        ))
        
        # Analytics Services
        self.register_service(ServiceEndpointConfig(
            service_name="clickhouse",
            service_type=ServiceType.ANALYTICS,
            namespace="olap-store",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=8123,
            local_port=8123,
            ingress_host="clickhouse.dpsc",
            health_check_path="/ping",
            tls_enabled=True,
            verify_ssl=False
        ))
        
        # Processing Services
        self.register_service(ServiceEndpointConfig(
            service_name="polycore-grpc",
            service_type=ServiceType.PROCESSING,
            namespace="mlops-toolchain",
            local_protocol=ProtocolScheme.GRPC,
            remote_protocol=ProtocolScheme.GRPCS,
            service_port=50051,
            local_port=50051,
            k8s_service_name="polycore",
            ingress_host="polycore.dpsc",
            require_tls=True,
            tls_enabled=True,
            verify_ssl=False,
            port_forward_type="service"
        ))
        
        self.register_service(ServiceEndpointConfig(
            service_name="polycore-rest",
            service_type=ServiceType.PROCESSING,
            namespace="mlops-toolchain",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=8080,
            local_port=8081,  # Different local port to avoid conflicts
            k8s_service_name="polycore",
            ingress_host="polycore-obs.dpsc",
            health_check_path="/health",
            tls_enabled=True,
            verify_ssl=False
        ))
        
        # Registry Services
        self.register_service(ServiceEndpointConfig(
            service_name="zot",
            service_type=ServiceType.REGISTRY,
            namespace="mlops-toolchain",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=5000,
            local_port=5000,
            ingress_host="zot.dpsc",
            health_check_path="/v2/",
            require_tls=True,
            tls_enabled=True,
            verify_ssl=False
        ))
        
        # MLFlow Processing
        self.register_service(ServiceEndpointConfig(
            service_name="mlflow",
            service_type=ServiceType.PROCESSING,
            namespace="processing",
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS,
            service_port=5000,
            local_port=5001,  # Different local port to avoid conflicts
            ingress_host="mlflow-processing.dpsc",
            health_check_path="/health",
            tls_enabled=True,
            verify_ssl=False
        ))
    
    def register_service(self, config: ServiceEndpointConfig) -> None:
        """Register a service configuration"""
        self._services[config.service_name] = config
    
    def get_service_config(self, service_name: str) -> ServiceEndpointConfig:
        """Get service configuration by name"""
        if service_name not in self._services:
            raise ValueError(f"Service '{service_name}' not found in registry")
        return self._services[service_name]
    
    def get_services_by_type(self, service_type: ServiceType) -> Dict[str, ServiceEndpointConfig]:
        """Get all services of a specific type"""
        return {
            name: config for name, config in self._services.items()
            if config.service_type == service_type
        }
    
    def get_services_by_namespace(self, namespace: str) -> Dict[str, ServiceEndpointConfig]:
        """Get all services in a specific namespace"""
        return {
            name: config for name, config in self._services.items()
            if config.namespace == namespace
        }
    
    def list_all_services(self) -> Dict[str, ServiceEndpointConfig]:
        """Get all registered services"""
        return self._services.copy()
    
    def validate_port_conflicts(self) -> Dict[int, list]:
        """Check for local port conflicts between services"""
        port_usage = {}
        for service_name, config in self._services.items():
            port = config.local_port
            if port not in port_usage:
                port_usage[port] = []
            port_usage[port].append(service_name)
        
        # Return only ports with conflicts
        return {port: services for port, services in port_usage.items() if len(services) > 1}


# Global service registry instance
service_registry = ServiceConfigRegistry()
