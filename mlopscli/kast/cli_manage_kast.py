"""cli manage_aks."""

import click
from click.core import Context
import click_config_file
from dependency_injector import providers
from dependency_injector.wiring import Provide
from dependency_injector.wiring import Provider
from dependency_injector.wiring import inject

from mlopscli.cli.options import ARTIFACTORY_TOKEN_OPTION
from mlopscli.cli.options import ARTIFACTORY_USERNAME_OPTION
from mlopscli.cli.options import add_options
from mlopscli.dependency_injection.containers import Application
from mlopscli.dependency_injection.containers import configure_application_container
from mlopscli.kast.manage_kast_installation import ManageKastInstallation
from mlopscli.utils.constants import INSTALLATION_LOCATIONS
from mlopscli.utils.log_header import log_full_host_header
from mlopscli.utils.logger import get_logger

logger = get_logger(__name__)


@click.group(name="kast")
def click_group() -> None:
    """Manage KAST"""
    pass


INSTALLATION_LOCATION = "installation_location"

RUNNER_TOKEN = "gitlab_runner_token"  # noqa: S105
RUNNER_TOKEN_OPTION = [
    click.option(
        f"--{RUNNER_TOKEN}",
        "-grt",
        envvar=f"{RUNNER_TOKEN.upper()}",
        help="gitlab runner token",
        type=str,
        default=None,
        required=False,
        show_default=True,
    )
]
INSTALLATION_LOCATION_OPTION = [
    click.option(
        f"--{INSTALLATION_LOCATION}",
        "-l",
        envvar=f"{INSTALLATION_LOCATION.upper()}",
        help=f"Installation location. Values {INSTALLATION_LOCATIONS} ",
        type=str,
        required=True,
    )
]


@click.command(name="install", short_help="install KAST")
@add_options(ARTIFACTORY_USERNAME_OPTION)
@add_options(ARTIFACTORY_TOKEN_OPTION)
@add_options(INSTALLATION_LOCATION_OPTION)
@add_options(RUNNER_TOKEN_OPTION)
@click_config_file.configuration_option(implicit=False)
@click.pass_context
@inject
def install_kast(
    ctx: Context,
    artifactory_username: str,
    artifactory_token: str,
    installation_location: str,
    gitlab_runner_token: str,
    config: providers.Configuration = Provider[Application.config],  # type: ignore
    **kwargs,  # pylint: disable=unused-argument  # noqa: ANN003
) -> int:
    """cli entrypoint."""
    installation_location = installation_location.lower()
    config.from_dict(ctx.params)
    configure_application_container(config, "manage_kast")
    return _install_kast(
        artifactory_username=artifactory_username,
        artifactory_token=artifactory_token,
        installation_location=installation_location,
        gitlab_runner_token=gitlab_runner_token,
    )


@inject
def _install_kast(
    artifactory_username: str,
    artifactory_token: str,
    installation_location: str,
    gitlab_runner_token: str,
    executor: ManageKastInstallation = Provide[Application.services.manage_kast_installation],  # type: ignore
) -> int:
    logger.info("started")
    log_full_host_header()
    executor.install_kast(
        artifactory_username=artifactory_username,
        artifactory_token=artifactory_token,
        installation_location=installation_location,
        gitlab_runner_token=gitlab_runner_token,
    )
    logger.info("ended")
    return 0


@click.command(name="uninstall", short_help="uninstall KAST. WARNING postgres and S3 data will be deleted")
@add_options(INSTALLATION_LOCATION_OPTION)
@click_config_file.configuration_option(implicit=False)
@click.pass_context
@inject
def uninstall_kast(
    ctx: Context,
    installation_location: str,
    config: providers.Configuration = Provider[Application.config],  # type: ignore
    **kwargs,  # noqa: ANN003
) -> int:
    """cli entrypoint."""
    config.from_dict(ctx.params)
    configure_application_container(config, "manage_kast")
    return _uninstall_kast(installation_location=installation_location)


def _uninstall_kast(
    installation_location: str,
    executor: ManageKastInstallation = Provide[Application.services.manage_kast_installation],  # type: ignore
) -> int:
    logger.info("started")
    log_full_host_header()
    executor.uninstall_kast(installation_location=installation_location)
    logger.info("ended")
    return 0
