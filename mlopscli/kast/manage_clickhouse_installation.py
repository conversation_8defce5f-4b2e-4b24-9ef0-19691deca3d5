import os
from pathlib import Path
from typing import Tuple

import yaml

from mlopscli.kast.manage_installation_base import KASTCTL_INSTALL_COMMAND
from mlopscli.kast.manage_installation_base import KASTCTL_UNINSTALL_COMMAND
from mlopscli.kast.manage_installation_base import ManageInstallationBaseClass
from mlopscli.utils.constants import KAST_WORKING_DIR
from mlopscli.utils.exceptions import SecretCreationException
from mlopscli.utils.kubernetes import create_docker_registry_secret
from mlopscli.utils.kubernetes import is_component_installed
from mlopscli.utils.kubernetes import is_helm_chart_installed
from mlopscli.utils.kubernetes import uninstall_k8s_component
from mlopscli.utils.kubernetes import wait_for_pod_with_prefix_to_run
from mlopscli.utils.minio import create_s3_bucket
from mlopscli.utils.system import generate_password
from mlopscli.utils.system import run_command

CLICKHOUSE_REGISTRY_SECRET_NAME = "kast-clickhouse-regcred"  # noqa: S105
CLICKHOUSE_HELM_CHART_NAME = "clickhouse"


class ClickHouse(ManageInstallationBaseClass):
    def __init__(self, namespace: str) -> None:
        super().__init__(logger_name=__name__, namespace=namespace)

    def install(
        self,
        s3_password: str,
        artifactory_username: str,
        artifactory_token: str,
        artifactory_url: str,
        s3_namespace: str,
        s3_service_name: str,
        s3_access_key: str,
    ) -> Tuple[str, str]:
        self._logger.info(f"Installing {CLICKHOUSE_HELM_CHART_NAME}...")
        clickhouse_mlops_username = "internal"
        clickhouse_mlops_password = None

        create_s3_bucket(
            service_name=s3_service_name,
            namespace=s3_namespace,
            s3_access_key=s3_access_key,
            s3_secret_key=s3_password,
            s3_host="localhost",
            s3_port=9000,
            bucket_name="ch-backup",
        )

        if not is_component_installed(type="namespace", name=self._namespace):
            run_command(f"kubectl create namespace {self._namespace}")

        if not create_docker_registry_secret(
            name=CLICKHOUSE_REGISTRY_SECRET_NAME,
            server=artifactory_url,
            artifactory_username=artifactory_username,
            artifactory_token=artifactory_token,
            namespace=self._namespace,
        ):
            raise SecretCreationException(secret_name=CLICKHOUSE_REGISTRY_SECRET_NAME, namespace=self._namespace)

        kpack_template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "kpack", "clickhouse", "kpack.yaml")
        kpack_file = os.path.join(KAST_WORKING_DIR, "kpack", "clickhouse", "kpack.yaml")
        kpack_dir = os.path.dirname(kpack_file)
        if os.path.exists(kpack_file):
            with open(kpack_file, "r") as file:
                kpack_template_content = yaml.safe_load(file)
                clickhouse_mlops_password = kpack_template_content["clickhouse"]["users"][clickhouse_mlops_username]["password"]
        else:
            with open(kpack_template_file, "r") as file:
                kpack_template_content = yaml.safe_load(file)
                if not os.path.exists(kpack_dir):
                    Path(kpack_dir).mkdir(parents=True, exist_ok=True)
                kpack_template_content["clickhouse"]["backup"]["s3"] = {}
                kpack_template_content["clickhouse"]["backup"]["s3"]["endpoint"] = f"https://{s3_service_name}.{s3_namespace}:9000"
                kpack_template_content["clickhouse"]["backup"]["s3"]["secret"] = s3_password
                kpack_template_content["clickhouse"]["users"] = {"admin": {}, "internal": {}, "user": {}, "web": {}}
                kpack_template_content["clickhouse"]["users"]["admin"]["password"] = generate_password()
                clickhouse_mlops_password = generate_password()
                kpack_template_content["clickhouse"]["users"][clickhouse_mlops_username]["password"] = clickhouse_mlops_password
                kpack_template_content["clickhouse"]["users"]["user"]["password"] = generate_password()
                kpack_template_content["clickhouse"]["users"]["web"]["password"] = generate_password()

                with open(kpack_file, "w") as file:
                    self._logger.debug(f"writing kpack file ({kpack_file})")
                    yaml.dump(kpack_template_content, file, default_flow_style=False)
                    file.close()

        if is_helm_chart_installed(CLICKHOUSE_HELM_CHART_NAME):
            self._logger.info(f"Helm chart ({CLICKHOUSE_HELM_CHART_NAME}) is already deployed...")
        else:
            self._logger.info(f"Deploying {CLICKHOUSE_HELM_CHART_NAME} kpack...")
            os.chdir(kpack_dir)
            command = KASTCTL_INSTALL_COMMAND
            run_command(command=command, check=True)

        wait_for_pod_with_prefix_to_run(namespace=self._namespace, pod_name_prefix=CLICKHOUSE_HELM_CHART_NAME)

        self._logger.info(f"Successfully installed {CLICKHOUSE_HELM_CHART_NAME}")

        return (clickhouse_mlops_username, clickhouse_mlops_password)

    def uninstall(self, delete_namespace: bool) -> None:
        self._logger.info(f"Uninstalling {CLICKHOUSE_HELM_CHART_NAME}...")
        kpack_dir = os.path.join(KAST_WORKING_DIR, "kpack", "clickhouse")
        if os.path.exists(kpack_dir):
            os.chdir(kpack_dir)
            run_command(KASTCTL_UNINSTALL_COMMAND)
        if delete_namespace:
            uninstall_k8s_component(type="namespace", name=self._namespace)
        self._logger.info(f"Uninstalling {CLICKHOUSE_HELM_CHART_NAME} Completed")
