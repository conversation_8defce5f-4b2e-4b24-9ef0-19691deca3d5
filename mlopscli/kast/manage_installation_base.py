from mlopscli.utils.logger import get_logger

CA_CERTIFICATES_CRT = "ca-certificates.crt"
CLUSTER_ISSUER = "cert-manager.io/cluster-issuer"
KASTCTL_INSTALL_COMMAND = "kastctl install --prompt=false"
KASTCTL_UNINSTALL_COMMAND = "kastctl uninstall"
NGINX_PROXY_BODY_SIZE = "nginx.ingress.kubernetes.io/proxy-body-size"


class ManageInstallationBaseClass:
    def __init__(self, logger_name: str, namespace: str) -> None:
        self._namespace = namespace
        self._logger = get_logger(logger_name)
