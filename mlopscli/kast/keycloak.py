import time

import requests
import urllib3

from mlopscli.utils.logger import get_logger
from mlopscli.utils.port_forward_thread import PORT_FORWARD_START_WAIT_TIME
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_SERVICE
from mlopscli.utils.port_forward_thread import PortForwardThread


class Keycloak:
    def __init__(
        self,
        keycloak_host: str,
        keycloak_port: int,
        keycloak_path: str,
        use_https: bool,
        admin_user: str,
        admin_password: str,
        secure_ssl: bool,
        timeout: int = 10,
        client_id: str = "admin-cli",
        realm: str = "master",
    ) -> None:
        self._logger = get_logger(__name__)
        protocol = "https" if use_https else "http"
        self._keycloak_url = f"{protocol}://{keycloak_host}:{keycloak_port}/{keycloak_path}"
        self._keycloak_port = keycloak_port
        self._admin_user = admin_user
        self._admin_password = admin_password
        self._secure_ssl = secure_ssl
        self._timeout = timeout
        self._client_id = client_id
        self._realm = realm
        self._pf_thread = None
        if not self._secure_ssl:
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    def get_access_token(self) -> str:
        token_url = f"{self._keycloak_url}/realms/master/protocol/openid-connect/token"
        payload = {"username": self._admin_user, "password": self._admin_password, "client_id": self._client_id, "grant_type": "password"}
        response = requests.post(token_url, data=payload, verify=self._secure_ssl, timeout=self._timeout)
        response.raise_for_status()  # Raise an error on a failed request
        return response.json()["access_token"]

    def does_user_exist(self, access_token: str, username: str) -> bool:
        get_user_url = f"{self._keycloak_url}/admin/realms/{self._realm}/users?username={username}"
        response = requests.get(get_user_url, headers={"Authorization": f"Bearer {access_token}"}, verify=self._secure_ssl, timeout=self._timeout)
        response.raise_for_status()  # Raise an error on a failed request
        users = response.json()
        return len(users) > 0  # Returns True if the user exists

    def create_user(self, access_token: str, username: str, password: str) -> None:
        if self.does_user_exist(access_token, username):
            self._logger.info(f"User {username} already exists, skipping creation.")
        else:
            user_url = f"{self._keycloak_url}/admin/realms/{self._realm}/users"
            user_data = {"username": username, "enabled": True, "email": f"{username}@example.com", "firstName": username, "lastName": "User"}

            # Create the user
            response = requests.post(
                user_url,
                json=user_data,
                headers={"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"},
                verify=self._secure_ssl,
                timeout=self._timeout,
            )
            response.raise_for_status()  # Raise an error on a failed request
            self._logger.info(f"User {username} created.")

            # Get the user ID
            get_user_url = f"{self._keycloak_url}/admin/realms/{self._realm}/users?username={username}"
            response = requests.get(get_user_url, headers={"Authorization": f"Bearer {access_token}"}, verify=self._secure_ssl, timeout=self._timeout)
            response.raise_for_status()  # Raise an error on a failed request
            user_id = response.json()[0]["id"]

            # Set the user's password
            reset_password_url = f"{self._keycloak_url}/admin/realms/{self._realm}/users/{user_id}/reset-password"
            password_data = {"type": "password", "value": password, "temporary": False}

            response = requests.put(
                reset_password_url,
                json=password_data,
                headers={"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"},
                verify=self._secure_ssl,
                timeout=self._timeout,
            )
            response.raise_for_status()  # Raise an error on a failed request
            self._logger.info(f"Password for {username} set.")

    def open_connection(self, namespace: str) -> None:
        if self._pf_thread is None:
            self._pf_thread = PortForwardThread(
                type=PORT_FORWARD_TYPE_SERVICE,
                name="keycloak-http",
                namespace=namespace,
                local_port=self._keycloak_port,
                remote_port=self._keycloak_port,
            )
            self._pf_thread.start()

            # Allow some time for the port-forwarding to establish
            time.sleep(PORT_FORWARD_START_WAIT_TIME)
            if not self._pf_thread.is_alive():
                self._logger.warning("Port forward command failed, but it could be due to port already being forwarded. Continuing execution.")
            else:
                self._logger.info("Port forwarding established.")

        else:
            self._logger.info("Connection is already opened")

    def close_connection(self) -> None:
        if self._pf_thread is not None:
            self._pf_thread.stop()
            self._pf_thread.join()
            self._pf_thread = None
        else:
            self._logger.info("Connection is already closed")
