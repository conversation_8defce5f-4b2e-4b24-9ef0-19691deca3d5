import os
from typing import Optional

from mlopscli.kast.manage_installation_base import ManageInstallationBaseClass
from mlopscli.utils.kubernetes import is_component_installed
from mlopscli.utils.kubernetes import is_helm_chart_installed
from mlopscli.utils.kubernetes import is_helm_repo_installed
from mlopscli.utils.kubernetes import uninstall_helm_chart
from mlopscli.utils.kubernetes import uninstall_helm_repo
from mlopscli.utils.kubernetes import uninstall_k8s_component
from mlopscli.utils.system import run_command
from mlopscli.utils.system import run_command_with_output

GITLAB_RUNNER_HELM_REPO_URL = "https://charts.gitlab.io"
GITLAB_RUNNER_HELM_REPO_NAME = "gitlab"
GITLAB_RUNNER_NAMESPACE = "gitlab-runner"
GITLAB_RUNNER_HELM_CHART_NAME = "gitlab-runner"
GITLAB_RUNNER_HELM_CHART_VERSION = "0.77.3"


class GitlabRunner(ManageInstallationBaseClass):
    def __init__(self, namespace: str) -> None:
        super().__init__(logger_name=__name__, namespace=namespace)

    def _add_repo(self) -> None:
        if is_helm_repo_installed(GITLAB_RUNNER_HELM_REPO_NAME):
            self._logger.info(f"{GITLAB_RUNNER_HELM_REPO_NAME} helm repo already installed...")
        else:
            self._logger.info(f"Adding helm repo ({GITLAB_RUNNER_HELM_REPO_NAME}): {GITLAB_RUNNER_HELM_REPO_URL}")
            run_command_with_output(f"helm repo add {GITLAB_RUNNER_HELM_REPO_NAME} {GITLAB_RUNNER_HELM_REPO_URL}")
            self._logger.info(f"Successfully added helm repo ({GITLAB_RUNNER_HELM_REPO_NAME})")

    def install(self, runner_token: Optional[str]) -> None:
        if runner_token is None:
            self._logger.info("No gitlab token received -> No gitlab runner will be installed.")
        else:
            self._logger.info("Starting to install gitlab-runner...")
            self._add_repo()

            if not is_component_installed(type="namespace", name=self._namespace):
                self._logger.info(f"Creating {self._namespace} namespace...")
                run_command(f"kubectl create namespace {self._namespace}")
                self._logger.info(f"Successfully created {self._namespace} namespace!")
            else:
                self._logger.info(f"{self._namespace} namespace already exists...")

            if is_helm_chart_installed(GITLAB_RUNNER_HELM_CHART_NAME):
                self._logger.info(f"Helm chart ({GITLAB_RUNNER_HELM_CHART_NAME}) is already deployed...")
            else:
                values_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "yaml", "gitlab-runner", "values.yaml")
                self._logger.info(f"Deploying helm chart: ({GITLAB_RUNNER_HELM_CHART_NAME})")
                command = f'helm install -f {values_file_path} {GITLAB_RUNNER_HELM_CHART_NAME} gitlab/gitlab-runner \
                        --version {GITLAB_RUNNER_HELM_CHART_VERSION} -n {self._namespace} --set runnerToken="{runner_token}"'
                self._logger.debug(f"Running command ({command})")
                run_command(command)

                self._logger.info(f"Successfully deployed {GITLAB_RUNNER_HELM_CHART_NAME}!")

            self._logger.info("gitlab-runner installation completed")

    def uninstall(self, delete_namespace: bool) -> None:
        self._logger.info(f"Uninstalling {GITLAB_RUNNER_HELM_CHART_NAME}...")
        uninstall_helm_chart(GITLAB_RUNNER_HELM_CHART_NAME, self._namespace)
        if delete_namespace:
            uninstall_k8s_component(type="namespace", name=self._namespace)
        uninstall_helm_repo(GITLAB_RUNNER_HELM_REPO_NAME)
        self._logger.info(f"{GITLAB_RUNNER_HELM_CHART_NAME} uninstall completed")
