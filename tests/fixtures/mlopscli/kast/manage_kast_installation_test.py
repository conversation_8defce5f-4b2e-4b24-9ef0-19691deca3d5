from io import StringIO
import os
from pathlib import Path
import subprocess
from unittest.mock import MagicMock
from unittest.mock import patch

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
import pytest
from pytest_bdd import given
from pytest_bdd import parsers
from pytest_bdd import scenarios
from pytest_bdd import then
from pytest_bdd import when

from mlopscli.kast.manage_kast_installation import POLYDB_POSTGRESQL_POD_NAME
from mlopscli.kast.manage_kast_installation import POSTGRESQL_NAMESPACE
from mlopscli.kast.manage_kast_installation import POSTGRESQL_PORT
from mlopscli.kast.manage_kast_installation import ManageKastInstallation
from mlopscli.utils.constants import ARCH_AMD64
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR
from mlopscli.utils.constants import KAST_WORKING_DIR
from mlopscli.utils.exceptions import UnsupportedArchitectureException
from mlopscli.utils.exceptions import UnsupportedInstallationLocationException
from mlopscli.utils.kubernetes import is_component_installed
from mlopscli.utils.port_forward_thread import PORT_FORWARD_TYPE_POD
from mlopscli.utils.port_forward_thread import PortForwardThread
from tests.fixtures.mlopscli.utils.kubernetes_test import COMMAND_KEY
from tests.fixtures.mlopscli.utils.kubernetes_test import REPOS_KEY
from tests.fixtures.mlopscli.utils.kubernetes_test import RESULT_KEY
from tests.fixtures.mlopscli.utils.kubernetes_test import SUBPROCESS_RUN_STR

scenarios("mlopscli/kast/manage_kast_installation.feature")

SUBPROCESS_CHECK_OUTPUT_STR = "subprocess.check_output"

PATH_KEY = "path"
ERROR_KEY = "error"
ARCH_KEY = "arch"
POSTGRESQL_PASSWORD_KEY = "postgresql_password"  # noqa: S105
ARTIFACTORY_USERNAME = "artifactory_username"
ARTIFACTORY_TOKEN = "artifactory_token"  # noqa: S105
HOST_FILE_KEY = "hosts_file"
LOCATION = ""
HOSTS_FILE = ""
HOSTNAME_KEY = "hostname"

VALID_VALUE = "valid"
INVALID_VALUE = "invalid"


@pytest.fixture
def context():
    return {
        PATH_KEY: INVALID_VALUE,
        COMMAND_KEY: None,
        REPOS_KEY: "",
        ARCH_KEY: "",
        POSTGRESQL_PASSWORD_KEY: INVALID_VALUE,
        ARTIFACTORY_USERNAME: INVALID_VALUE,
        ARTIFACTORY_TOKEN: INVALID_VALUE,
        LOCATION: "",
        HOSTS_FILE: "",
        HOSTNAME_KEY: "",
    }


@pytest.fixture
def dummy_ip():
    return "***********"


@pytest.fixture
def isolated_filesystem():
    runner = CliRunner()
    print(os.getcwd())
    with runner.isolated_filesystem():
        print(os.getcwd())
        directory = os.getcwd()
        print(os.getcwd())
        yield directory


@when("I check requirements")
def _check_requirements(isolated_filesystem, context) -> None:
    try:
        fleche_dir = os.path.join(os.getcwd(), "fleche")
        Path(fleche_dir).mkdir(parents=True)
        manager = ManageKastInstallation()
        context[RESULT_KEY] = manager._check_requirements(fleche_dir=fleche_dir, installation_location=INSTALLATION_LOCATION_LOCAL_STR)
    except Exception as e:
        context[ERROR_KEY] = e


@when("I print etc host info")
def _print_etc_host_info(context, dummy_ip) -> None:
    try:
        with patch("sys.stdout", new_callable=StringIO) as mock_stdout:
            manager = ManageKastInstallation()
            manager._update_etc_hosts_info(dummy_ip)
            context[RESULT_KEY] = mock_stdout.getvalue()
            print(context[RESULT_KEY])
    except Exception as e:
        context[ERROR_KEY] = e


@then(parsers.parse('I should see the expected output "{expected_output}"'))
def _step_then_should_see_expected_output(context, expected_output):
    assert expected_output in context[RESULT_KEY], f"Output does not contain '{expected_output}'. Found: {context[RESULT_KEY]}"


@when("I fill env")
def _fill_env(context) -> None:
    try:
        manager = ManageKastInstallation()
        context[RESULT_KEY] = manager.fill_env("toto")
    except Exception as e:
        context[ERROR_KEY] = e


@then(parsers.parse('env contain "{expected_variable}"'))
def _env_contain(context, expected_variable):
    assert context[RESULT_KEY].get(expected_variable, None) is not None, f"Env contain '{expected_variable}'. Found: {context[RESULT_KEY]}"


@when(parsers.parse('I generate hosts file with artifactory username "{artifactory_username}"'))
def _generate_hosts_file(isolated_filesystem, context, artifactory_username) -> None:
    try:
        kast_dir = os.path.join(os.getcwd(), "kast")
        Path(kast_dir).mkdir(parents=True)
        hosts_file = os.path.join(kast_dir, "hosts")
        context[HOST_FILE_KEY] = hosts_file
        manager = ManageKastInstallation()
        manager._generate_hosts_file(
            arch=ARCH_AMD64,
            installation_location=INSTALLATION_LOCATION_LOCAL_STR,
            hosts_file=hosts_file,
            hostname="toto",
            artifactory_username=artifactory_username,
            artifactory_token="token",  # noqa: S106
            postgresql_password="tata",  # noqa: S106
        )  # noqa: S106
    except Exception as e:
        context[ERROR_KEY] = e


@then(parsers.parse('file content "{expected_content}"'))
def _file_contain(context, expected_content):
    with open(context[HOST_FILE_KEY], "r", encoding="utf-8") as file:
        content = file.read()
        assert expected_content in content


@when(parsers.parse('I call is component installed of type "{type}", named "{name}" and on namespace "{namespace}"'))
def _is_component_installed(context, type, name, namespace) -> None:
    try:
        is_component_installed(type=type, name=name, namespace=namespace)
    except Exception as e:
        context[ERROR_KEY] = e


@then(parsers.parse('the answer is "{expected_answer}"'))
def _check_answer(context, expected_answer):
    if expected_answer == "True":
        expected_answer = True
    else:
        expected_answer = False

    assert context[RESULT_KEY] == expected_answer


@given(parsers.parse('a Kubernetes component of type "{type}" with name "{name}" in namespace "{namespace}" exists'))
def _component_exists(context, type, name, namespace):
    with patch(SUBPROCESS_RUN_STR) as mock_run:
        mock_run.return_value = MagicMock(stdout=f"{name}\n", stderr="", returncode=0)
        context[RESULT_KEY] = is_component_installed(type, name, namespace)


@given(parsers.parse('a Kubernetes component of type "{type}" with name "{name}" without namespace scope exists'))
def _component_exists_without_namespace_scope(context, type, name):
    with patch(SUBPROCESS_RUN_STR) as mock_run:
        mock_run.return_value = MagicMock(stdout=f"{name}\n", stderr="", returncode=0)
        context[RESULT_KEY] = is_component_installed(type, name, "")


@given(parsers.parse('a Kubernetes component of type "{type}" with name "{name}" in namespace "{namespace}" does not exist'))
def _component_does_not_exist(context, type, name, namespace):
    with patch(SUBPROCESS_RUN_STR) as mock_run:
        mock_run.side_effect = subprocess.CalledProcessError(returncode=1, cmd="kubectl")
        context[RESULT_KEY] = is_component_installed(type, name, namespace)


@then(parsers.parse('it calls the "{name}" command'))
def _command_was_called(context, name):
    # are we checking a specific command or whatever was logged in COMMAND?
    key = COMMAND_KEY
    if name in context:
        key = name

    assert context[key].called


@then(parsers.parse('it passed "{argument}" to the "{name}" command'))
def _command_called_with_arguments(context, argument, name):
    _command_was_called(context, name)

    # are we checking a specific command or whatever was logged in COMMAND?
    key = COMMAND_KEY
    if name in context:
        key = name

    args = context[key].call_args_list
    assert argument in str(args), f"{argument} not found in {args} of command {key}"


@given(parsers.parse("{arch} as my architecture"))
def _set_architecture(context, arch):
    context[ARCH_KEY] = arch


@given("an unsupported architecture")
def _set_invalid_architecture(context):
    context[ARCH_KEY] = INVALID_VALUE


@given("a valid postgresql password")
def _set_postgresql_password_as_valid(context):
    context[POSTGRESQL_PASSWORD_KEY] = VALID_VALUE


@given("an invalid postgresql password")
def _set_postgresql_password_as_invalid(context):
    context[POSTGRESQL_PASSWORD_KEY] = INVALID_VALUE


@given("missing dbtool binaries")
def _set_path_as_inexisting(context):
    context[PATH_KEY] = INVALID_VALUE


@given("dbtool's binaries are installed")
def _set_path_as_existing(context):
    context[PATH_KEY] = VALID_VALUE


@given(parsers.parse('dbtool binaries are installed into "{path}"'))
def _set_existing_binary(context, path):
    context[PATH_KEY] = path


@when("I try to handle PolyDB's setup")
def _call_polydb_setup(context):
    try:
        with patch.object(ManageKastInstallation, "_run_polydb_executable"):
            with patch.object(ManageKastInstallation, "_polydb_executable_post_run"):
                with patch.object(ManageKastInstallation, "_polydb_executable_pre_run") as mock_pre_run:
                    manager = ManageKastInstallation()
                    manager._handle_polydb_setup(
                        arch=context[ARCH_KEY],
                        postgresql_password=context[POSTGRESQL_PASSWORD_KEY],
                        artifactory_username=context[ARTIFACTORY_USERNAME],
                        artifactory_token=context[ARTIFACTORY_TOKEN],
                    )
                    context[COMMAND_KEY] = mock_pre_run
    except Exception as e:
        context[ERROR_KEY] = e


@when("I run PolyDB's pre runs")
def _call_polydb_pre_run(context):
    binaries_exists = context[PATH_KEY] == VALID_VALUE

    with patch("os.path.exists", return_value=binaries_exists):
        with patch("time.sleep") as mock_time_sleep:
            with patch("mlopscli.kast.manage_kast_installation.create_postgres_database") as mock_create_database:
                with patch.object(PortForwardThread, "start") as mock_portforward_start:
                    port_forward_thread = PortForwardThread(
                        type=PORT_FORWARD_TYPE_POD,
                        name=POLYDB_POSTGRESQL_POD_NAME,
                        namespace=POSTGRESQL_NAMESPACE,
                        local_port=POSTGRESQL_PORT,
                        remote_port=POSTGRESQL_PORT,
                    )
                    manager = ManageKastInstallation()
                    try:
                        manager._polydb_executable_pre_run(context[POSTGRESQL_PASSWORD_KEY], port_forward_thread)
                    except SystemExit as e:
                        context[ERROR_KEY] = e
                        with pytest.raises(SystemExit):
                            # Sonar absolutely wants SystemExit exceptions to be raised if catched...
                            raise e
                    except Exception as e:
                        context[ERROR_KEY] = e

                    context["create_postgres_database"] = mock_create_database
                    context["port_forward_thread.start"] = mock_portforward_start
                    context["time.sleep"] = mock_time_sleep


@when("I run PolyDB run method")
def _call_polydb_run(context):
    binaries_exists = context[PATH_KEY] == VALID_VALUE

    # Build the executable and installed path.
    architecture = context[ARCH_KEY]
    executable = ""

    if architecture == ARCH_AMD64:
        executable = "dbtool_linux_amd64"
    else:
        executable = "dbtool_darwin_arm64"
    installed_at = os.path.join(KAST_WORKING_DIR, executable)

    with patch("os.chdir", return_value=binaries_exists) as mock_chdir:
        with patch(SUBPROCESS_RUN_STR) as mock_run:
            return_code = 0
            # if the prostgres password is wrong, we expect an error from the executable.
            if context[POSTGRESQL_PASSWORD_KEY] == INVALID_VALUE:
                return_code = 127

            mock_run.return_value = MagicMock(stdout=b"[GO]: vtest\n", stderr=b"", returncode=return_code)
            manager = ManageKastInstallation()
            try:
                manager._run_polydb_executable(full_executable_path=installed_at, postgresql_password=context[POSTGRESQL_PASSWORD_KEY])
            except SystemExit as e:
                context[ERROR_KEY] = e
                with pytest.raises(SystemExit):
                    # Sonar absolutely wants SystemExit exceptions to be raised if catched...
                    raise e
            except Exception as e:
                context[ERROR_KEY] = e
            context[SUBPROCESS_RUN_STR] = mock_run
            context["os.chdir"] = mock_chdir


@when("I run PolyDB post run method")
def _call_post_run_polydb(context):
    with patch.object(PortForwardThread, "stop") as mock_portforward_stop:
        with patch.object(PortForwardThread, "join") as mock_portforward_join:
            port_forward_thread = PortForwardThread(
                type=PORT_FORWARD_TYPE_POD,
                name=POLYDB_POSTGRESQL_POD_NAME,
                namespace=POSTGRESQL_NAMESPACE,
                local_port=POSTGRESQL_PORT,
                remote_port=POSTGRESQL_PORT,
            )
            manager = ManageKastInstallation()
            manager._polydb_executable_post_run(port_forward_thread)

            context["port_forward_thread.stop"] = mock_portforward_stop
            context["port_forward_thread.join"] = mock_portforward_join


@then("no exceptions happen")
def _check_for_no_exception(context):
    assert ERROR_KEY not in context, "No errors should've been logged during execution!"


@then("an exception is raised")
def _check_for_any_exception(context):
    assert ERROR_KEY in context, "No exceptions were catched!"


@then("I get an unsupported arch error")
def _arch_is_unsupported(context):
    error = context[ERROR_KEY]

    _check_for_any_exception(context)
    assert isinstance(error, UnsupportedArchitectureException), "Exception should have been of type UnsupportedArchitectureException."
    assert error.args == UnsupportedArchitectureException(context[ARCH_KEY]).args, "Error arguments don't match what's expected!"


@then(parsers.parse("the script stops with exit code {code}"))
def _check_for_sysexit(context, code):
    error = context[ERROR_KEY]

    _check_for_any_exception(context)
    assert isinstance(error, SystemExit), "Expected the script to raise a system exit."
    assert error.args == SystemExit(int(code)).args, "Error arguments don't match what's expected!"


@given(parsers.parse('the installation location is "{location}"'))
def _check_installation_location(context, location):
    context[LOCATION] = location


@when("calling _host_file_per_location with the installation location")
def _host_file_per_location(context):
    manager = ManageKastInstallation()

    try:
        # Don't set arch from returned values since it is based of host arch and not deterministic
        context[HOSTS_FILE], _ = manager._host_file_per_location(context[LOCATION])
    except UnsupportedInstallationLocationException as e:
        context[ERROR_KEY] = e


@then(parsers.parse('the returned hosts file should be "{hosts_file}" and architecture should be "{arch}"'))
def _host_file_returned(context, hosts_file, arch):
    assert context[HOSTS_FILE] == os.path.join(KAST_WORKING_DIR, hosts_file)
    assert context[ARCH_KEY] == arch


@then("an exception of type UnsupportedInstallationLocationException should be raised")
def _location_unsupported(context):
    error = context[ERROR_KEY]
    assert isinstance(error, UnsupportedInstallationLocationException)


@given(parsers.parse("the machine architecture is {arch}"))
def step_given_machine_architecture(context, arch):
    context[ARCH_KEY] = arch


@given(parsers.parse("the hostname is {hostname}"))
def step_given_hostname(context, hostname):
    context[HOSTNAME_KEY] = hostname


@when("installing the SSH key")
def step_install_ssh_key(context):
    installer = ManageKastInstallation()

    try:
        installer._install_ssh_key(context[ARCH_KEY], context[HOSTNAME_KEY])
        context[ERROR_KEY] = None
    except UnsupportedArchitectureException as ex:
        context[ERROR_KEY] = ex


@then("the SSH key is generated if it doesn't exist")
def step_check_key_generation(context):
    ssh_public_key_file = os.path.expanduser("~/.ssh/id_rsa.pub")
    assert os.path.exists(ssh_public_key_file), "SSH public key should exist."


@then("it is transferred to the ARM64 machine")
def step_check_transfer_arm64(context):
    # Check relevant steps or mocks to ensure command execution
    pass  # Mock or actual checks for multipass transfer command


@then("the authorized_keys file is updated")
def step_check_authorized_keys_update(context):
    # Check relevant steps or mocks to ensure authorized_keys update
    pass  # Mock or actual checks for authorized_keys update


@then("it is appended to the authorized_keys file on the host machine")
def step_check_authorized_keys_appended(context):
    test_dir = "tests/data/kast/ssh_key"
    authorized_keys_path = os.path.join(test_dir, "authorized_keys")
    ssh_public_key_file = os.path.join(test_dir, "test_rsa.pub")

    # Make sure the test folder exists
    os.makedirs(test_dir, exist_ok=True)

    # Generate test SSH key files if they do not exist
    if not os.path.exists(ssh_public_key_file):
        os.system(f'ssh-keygen -t rsa -b 2048 -f {os.path.join(test_dir, "test_rsa")} -N ""')  # noqa: S605

    # Append the public key to the authorized_keys file for testing
    with open(ssh_public_key_file, "r") as f:
        ssh_key_content = f.read()

    with open(authorized_keys_path, "a") as f:
        f.write(ssh_key_content)

    # Read the authorized_keys file to check if the SSH key is appended
    with open(authorized_keys_path, "r") as f:
        authorized_keys_content = f.read()

    assert ssh_key_content in authorized_keys_content, "SSH key should be appended to authorized_keys."

    # Clean up the test directory if necessary (optional)
    if os.path.exists(os.path.join(test_dir, "test_rsa")):
        os.remove(os.path.join(test_dir, "test_rsa"))
    if os.path.exists(ssh_public_key_file):
        os.remove(ssh_public_key_file)


@then("an UnsupportedArchitectureException is raised")
def step_check_unsupported_architecture_exception(context):
    error = context[ERROR_KEY]
    assert error is not None, "Exception should be raised for unsupported architecture."
    assert isinstance(error, UnsupportedArchitectureException), "The exception should be UnsupportedArchitectureException."
