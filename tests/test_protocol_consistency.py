"""Tests for protocol consistency across local and remote deployments

This test suite validates the MCStack-compliant protocol handling implementation
to ensure consistent behavior across deployment environments.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import threading
import time

from mlopscli.utils.service_config import (
    ServiceEndpointConfig, ServiceConfigRegistry, ProtocolScheme, ServiceType,
    service_registry
)
from mlopscli.utils.installation_resolver import In<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ServiceEndpoint
from mlopscli.utils.service_clients import ServiceClientFactory, get_service_client_factory
from mlopscli.utils.constants import INSTALLATION_LOCATION_LOCAL_STR, INSTALLATION_LOCATION_REMOTE_STR
from mlopscli.utils.port_forward_thread import PortForwardThread
from mlopscli.utils.logger import get_logger


class TestServiceConfiguration:
    """Test service configuration registry functionality"""
    
    def test_service_config_creation(self):
        """Test creating service configuration"""
        config = ServiceEndpointConfig(
            service_name="test-service",
            service_type=ServiceType.PROCESSING,
            namespace="test-namespace",
            service_port=8080,
            local_port=8080
        )
        
        assert config.service_name == "test-service"
        assert config.service_type == ServiceType.PROCESSING
        assert config.namespace == "test-namespace"
        assert config.service_port == 8080
        assert config.local_port == 8080
        assert config.local_protocol == ProtocolScheme.HTTP
        assert config.remote_protocol == ProtocolScheme.HTTPS
    
    def test_protocol_resolution(self):
        """Test protocol resolution for different deployment locations"""
        config = ServiceEndpointConfig(
            service_name="test-service",
            service_type=ServiceType.PROCESSING,
            namespace="test-namespace",
            service_port=8080,
            local_port=8080,
            local_protocol=ProtocolScheme.HTTP,
            remote_protocol=ProtocolScheme.HTTPS
        )
        
        assert config.get_protocol_for_location(INSTALLATION_LOCATION_LOCAL_STR) == ProtocolScheme.HTTP
        assert config.get_protocol_for_location(INSTALLATION_LOCATION_REMOTE_STR) == ProtocolScheme.HTTPS
        
        with pytest.raises(ValueError):
            config.get_protocol_for_location("invalid")
    
    def test_service_registry(self):
        """Test service registry functionality"""
        registry = ServiceConfigRegistry()
        
        # Test that default services are loaded
        assert "keycloak" in registry.list_all_services()
        assert "minio" in registry.list_all_services()
        assert "lakefs" in registry.list_all_services()
        
        # Test getting service config
        keycloak_config = registry.get_service_config("keycloak")
        assert keycloak_config.service_name == "keycloak"
        assert keycloak_config.service_type == ServiceType.AUTHENTICATION
        
        # Test service not found
        with pytest.raises(ValueError):
            registry.get_service_config("nonexistent-service")
    
    def test_port_conflict_detection(self):
        """Test port conflict detection"""
        registry = ServiceConfigRegistry()
        conflicts = registry.validate_port_conflicts()
        
        # Should not have conflicts in default configuration
        assert len(conflicts) == 0
        
        # Add a conflicting service
        registry.register_service(ServiceEndpointConfig(
            service_name="conflict-service",
            service_type=ServiceType.PROCESSING,
            namespace="test",
            service_port=9000,
            local_port=8080  # Conflicts with keycloak
        ))
        
        conflicts = registry.validate_port_conflicts()
        assert 8080 in conflicts
        assert len(conflicts[8080]) == 2


class TestInstallationResolver:
    """Test installation resolver functionality"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.logger = get_logger("test")
        self.resolver = InstallationResolver(self.logger)
    
    def test_service_endpoint_creation(self):
        """Test ServiceEndpoint creation and properties"""
        endpoint = ServiceEndpoint(
            host="127.0.0.1",
            port=8080,
            protocol=ProtocolScheme.HTTP,
            verify_ssl=False,
            timeout=30
        )
        
        assert endpoint.host == "127.0.0.1"
        assert endpoint.port == 8080
        assert endpoint.protocol == ProtocolScheme.HTTP
        assert endpoint.url == "http://127.0.0.1:8080"
        assert not endpoint.verify_ssl
        assert endpoint.timeout == 30
    
    @patch('mlopscli.utils.installation_resolver.subprocess.run')
    def test_service_verification(self, mock_run):
        """Test service existence verification"""
        # Test service exists
        mock_run.return_value.returncode = 0
        assert self.resolver._verify_service_exists("test-service", "test-namespace")
        
        # Test service doesn't exist
        mock_run.return_value.returncode = 1
        assert not self.resolver._verify_service_exists("test-service", "test-namespace")
    
    def test_local_endpoint_resolution(self):
        """Test local endpoint resolution"""
        endpoint = self.resolver.resolve_service_endpoint("keycloak", INSTALLATION_LOCATION_LOCAL_STR)
        
        assert endpoint.host == "127.0.0.1"
        assert endpoint.port == 8080
        assert endpoint.protocol == ProtocolScheme.HTTP
        assert endpoint.url == "http://127.0.0.1:8080"
    
    @patch('mlopscli.utils.installation_resolver.InstallationResolver._create_port_forward')
    def test_remote_endpoint_resolution(self, mock_create_pf):
        """Test remote endpoint resolution with port forwarding"""
        # Mock successful port forward
        mock_pf_thread = Mock()
        mock_pf_thread.is_alive.return_value = True
        mock_create_pf.return_value = mock_pf_thread
        
        endpoint = self.resolver.resolve_service_endpoint("keycloak", INSTALLATION_LOCATION_REMOTE_STR)
        
        assert endpoint.host == "127.0.0.1"
        assert endpoint.port == 8080
        assert endpoint.protocol == ProtocolScheme.HTTPS
        assert endpoint.url == "https://127.0.0.1:8080"
        
        # Verify port forward was created
        mock_create_pf.assert_called_once()
    
    def test_cleanup_functionality(self):
        """Test endpoint cleanup"""
        # Create a mock endpoint with cleanup
        mock_endpoint = Mock()
        self.resolver._active_endpoints["test_local"] = mock_endpoint
        
        # Test specific cleanup
        self.resolver.cleanup_service_endpoint("test", "local")
        mock_endpoint.cleanup.assert_called_once()
        assert "test_local" not in self.resolver._active_endpoints
        
        # Test cleanup all
        self.resolver._active_endpoints["test2_remote"] = Mock()
        self.resolver.cleanup_all_endpoints()
        assert len(self.resolver._active_endpoints) == 0


class TestServiceClientFactory:
    """Test service client factory functionality"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.logger = get_logger("test")
        self.factory = ServiceClientFactory(self.logger)
    
    @patch('mlopscli.utils.service_clients.ServiceClientFactory.create_keycloak_client')
    def test_keycloak_client_creation(self, mock_create):
        """Test Keycloak client creation"""
        mock_client = Mock()
        mock_create.return_value = mock_client
        
        client = self.factory.create_keycloak_client(
            installation_location=INSTALLATION_LOCATION_LOCAL_STR,
            admin_user="admin",
            admin_password="password"
        )
        
        assert client == mock_client
        mock_create.assert_called_once()
    
    def test_generic_http_client_creation(self):
        """Test generic HTTP client creation"""
        with patch.object(self.factory._resolver, 'resolve_service_endpoint') as mock_resolve:
            mock_endpoint = ServiceEndpoint(
                host="127.0.0.1",
                port=8080,
                protocol=ProtocolScheme.HTTP
            )
            mock_resolve.return_value = mock_endpoint
            
            config = self.factory.create_generic_http_client(
                service_name="test-service",
                installation_location=INSTALLATION_LOCATION_LOCAL_STR
            )
            
            assert config["base_url"] == "http://127.0.0.1:8080"
            assert config["host"] == "127.0.0.1"
            assert config["port"] == 8080
            assert config["protocol"] == "http"
    
    def test_client_lifecycle_management(self):
        """Test client lifecycle management"""
        # Mock endpoint resolution
        with patch.object(self.factory._resolver, 'resolve_service_endpoint') as mock_resolve:
            mock_endpoint = Mock()
            mock_resolve.return_value = mock_endpoint
            
            # Create client
            self.factory.create_generic_http_client("test", INSTALLATION_LOCATION_LOCAL_STR)
            
            # Verify client is tracked
            assert "test_local" in self.factory._active_clients
            
            # Test cleanup
            self.factory.cleanup_client("test", INSTALLATION_LOCATION_LOCAL_STR)
            assert "test_local" not in self.factory._active_clients
            mock_endpoint.cleanup.assert_called_once()


class TestPortForwardThread:
    """Test enhanced port forward thread functionality"""
    
    def test_port_forward_initialization(self):
        """Test port forward thread initialization"""
        pf_thread = PortForwardThread(
            type="service",
            name="test-service",
            namespace="test-namespace",
            local_port=8080,
            remote_port=8080
        )
        
        assert pf_thread._type == "service"
        assert pf_thread._name == "test-service"
        assert pf_thread._namespace == "test-namespace"
        assert pf_thread._local_port == 8080
        assert pf_thread._remote_port == 8080
    
    def test_command_building(self):
        """Test kubectl command building"""
        pf_thread = PortForwardThread(
            type="service",
            name="test-service",
            namespace="test-namespace",
            local_port=8080,
            remote_port=8080
        )
        
        cmd = pf_thread._build_command()
        expected = [
            "kubectl", "port-forward", "--address", "127.0.0.1",
            "service/test-service", "8080:8080", "-n", "test-namespace"
        ]
        
        assert cmd == expected
    
    def test_invalid_type_handling(self):
        """Test handling of invalid port forward types"""
        pf_thread = PortForwardThread(
            type="invalid",
            name="test-service",
            namespace="test-namespace",
            local_port=8080,
            remote_port=8080
        )
        
        with pytest.raises(ValueError):
            pf_thread._build_command()


class TestProtocolConsistency:
    """Integration tests for protocol consistency"""
    
    def test_local_vs_remote_protocol_consistency(self):
        """Test that protocols are consistently applied across deployments"""
        logger = get_logger("test")
        resolver = InstallationResolver(logger)
        
        # Test local deployment
        with patch.object(resolver, '_verify_connection', return_value=True):
            local_endpoint = resolver.resolve_service_endpoint("keycloak", INSTALLATION_LOCATION_LOCAL_STR)
            assert local_endpoint.protocol == ProtocolScheme.HTTP
            assert local_endpoint.host == "127.0.0.1"
        
        # Test remote deployment (mock port forwarding)
        with patch.object(resolver, '_create_port_forward') as mock_pf:
            mock_pf.return_value = Mock()
            with patch.object(resolver, '_verify_connection', return_value=True):
                remote_endpoint = resolver.resolve_service_endpoint("keycloak", INSTALLATION_LOCATION_REMOTE_STR)
                assert remote_endpoint.protocol == ProtocolScheme.HTTPS
                assert remote_endpoint.host == "127.0.0.1"  # Port forward uses localhost
    
    def test_service_registry_completeness(self):
        """Test that all expected services are configured"""
        expected_services = [
            "keycloak", "lakefs", "minio", "minio-console",
            "grafana", "prometheus", "clickhouse", "polycore-grpc",
            "polycore-rest", "zot", "mlflow"
        ]
        
        registry_services = service_registry.list_all_services()
        
        for service in expected_services:
            assert service in registry_services, f"Service {service} not found in registry"
    
    def test_global_factory_singleton(self):
        """Test global factory singleton behavior"""
        factory1 = get_service_client_factory()
        factory2 = get_service_client_factory()
        
        assert factory1 is factory2  # Should be the same instance
